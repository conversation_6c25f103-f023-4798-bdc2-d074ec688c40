/*
 * Copyright 2001 Perforce Software.  All rights reserved.
 *
 */

/*
 * simplecvt.cc - Character set conversion code base class
 *
 * This is seporate from basecvt.cc so that unless you are really
 * possibly going to do character set conversions you will not link
 * in this code.
 */

#include <stdhdrs.h>

#include "i18napi.h"
#include "charcvt.h"
#include "charman.h"


CharSetCvt *
CharSetCvtUTF8toSimple::Clone()
{
	return new CharSetCvtUTF8toSimple(charinfo);
}

CharSetCvt *
CharSetCvtUTF8toSimple::ReverseCvt()
{
	return new CharSetCvtSimpletoUTF8(charinfo);
}

int
CharSetCvtUTF8toSimple::Cvt(const char **sourcestart,
			    const char *sourceend,
			    char **targetstart,
			    char *targetend)
{
	const MapEnt *map = charinfo->toMap;
	int mapsize = charinfo->toMapSize;
	unsigned int v, oldv;

	while (*sourcestart < sourceend && *targetstart < targetend)
	{
	    v = **sourcestart & 0xff;
	    int l;
	    if (v & 0x80)
	    {
		l = bytesFromUTF8[v];
		if (l + *sourcestart >= sourceend)
		{
		    lasterr = PARTIALCHAR;
		    return 0;
		}
		switch (l)
		{
		case 2:
		    v <<= 6;
		    v += 0xff & *++*sourcestart;
		case 1:
		    v <<= 6;
		    v += 0xff & *++*sourcestart;
		    v -= offsetsFromUTF8[l];
# ifdef STRICT_UTF8
		    if( v < minimumFromUTF8[l] )
		    {
			// illegal over long UTF8 sequence
			lasterr = NOMAPPING;
			*sourcestart -= l;
			return 0;
		    }
# endif
		    // at this point v is UCS 2
		    oldv = v;
		    if (v >= 0x80)
			v = MapThru(v, map, mapsize, 0xfffd);
		    if (v < 0x100)
		    {
			**targetstart = v;
			break;
		    }
		    if (checkBOM && oldv == 0xfeff)
		    {
			// suppress BOM
			checkBOM = 0;
			++*sourcestart;
			continue;
		    }
		    *sourcestart -= l;
		    // note fall through
		default:
		    lasterr = NOMAPPING;
		    return 0;
		}
	    }
	    else
	    {
		**targetstart = v;
	    }
	    ++charcnt;
	    if( v == '\n' ) {
		++linecnt;
		charcnt = 0;
	    }
	    ++*sourcestart;
	    ++*targetstart;
	    checkBOM = 0;
	}    
	return 0;
}

CharSetCvt *
CharSetCvtSimpletoUTF8::Clone()
{
	return new CharSetCvtSimpletoUTF8(charinfo);
}

CharSetCvt *
CharSetCvtSimpletoUTF8::ReverseCvt()
{
	return new CharSetCvtUTF8toSimple(charinfo);
}

int
CharSetCvtSimpletoUTF8::Cvt(const char **sourcestart,
			    const char *sourceend,
			    char **targetstart,
			    char *targetend)
{
	const unsigned short *map = charinfo->fromMap;
	int off = charinfo->fromOffset;
	unsigned int v;

	while (*sourcestart < sourceend && *targetstart < targetend)
	{
	    v = **sourcestart & 0xff;
	    if (v & 0x80)
	    {
		if( v < off )
		{
		    lasterr = NOMAPPING;
		    break;
		}
		v = map[ v - off ];
		// emit UTF8 of v
		if (v >= 0x800)
		{
		    if (v == 0xfffd)
		    {
			// 0xfffd is specified by unicode as a replacement
			// character when a mapping fails... seems good here
			lasterr = NOMAPPING;
			break;
		    }
		
		    if (2 + *targetstart >= targetend)
		    {
			lasterr = PARTIALCHAR;
			break;
		    }
		    **targetstart = 0xe0 | (v >> 12);
		    *++*targetstart = 0x80 | ((v >> 6) & 0x3f);
		    *++*targetstart = 0x80 | (v & 0x3f);
		}
		else // v >= 0x80
		{
		    if (1 + *targetstart >= targetend)
		    {
			lasterr = PARTIALCHAR;
			break;
		    }
		    **targetstart = 0xc0 | (v >> 6);
		    *++*targetstart = 0x80 | (v & 0x3f);
		}
	    }
	    else
		**targetstart = v;
	    ++charcnt;
	    if( v == '\n' ) {
		++linecnt;
		charcnt = 0;
	    }
	    ++*targetstart;
	    ++*sourcestart;
	}
	return 0;
}

static const unsigned short from437[] = {
    0x00c7,	// 0x80
    0x00fc,	// 0x81
    0x00e9,	// 0x82
    0x00e2,	// 0x83
    0x00e4,	// 0x84
    0x00e0,	// 0x85
    0x00e5,	// 0x86
    0x00e7,	// 0x87
    0x00ea,	// 0x88
    0x00eb,	// 0x89
    0x00e8,	// 0x8a
    0x00ef,	// 0x8b
    0x00ee,	// 0x8c
    0x00ec,	// 0x8d
    0x00c4,	// 0x8e
    0x00c5,	// 0x8f
    0x00c9,	// 0x90
    0x00e6,	// 0x91
    0x00c6,	// 0x92
    0x00f4,	// 0x93
    0x00f6,	// 0x94
    0x00f2,	// 0x95
    0x00fb,	// 0x96
    0x00f9,	// 0x97
    0x00ff,	// 0x98
    0x00d6,	// 0x99
    0x00dc,	// 0x9a
    0x00a2,	// 0x9b
    0x00a3,	// 0x9c
    0x00a5,	// 0x9d
    0x20a7,	// 0x9e
    0x0192,	// 0x9f
    0x00e1,	// 0xa0
    0x00ed,	// 0xa1
    0x00f3,	// 0xa2
    0x00fa,	// 0xa3
    0x00f1,	// 0xa4
    0x00d1,	// 0xa5
    0x00aa,	// 0xa6
    0x00ba,	// 0xa7
    0x00bf,	// 0xa8
    0x2310,	// 0xa9
    0x00ac,	// 0xaa
    0x00bd,	// 0xab
    0x00bc,	// 0xac
    0x00a1,	// 0xad
    0x00ab,	// 0xae
    0x00bb,	// 0xaf
    0x2591,	// 0xb0
    0x2592,	// 0xb1
    0x2593,	// 0xb2
    0x2502,	// 0xb3
    0x2524,	// 0xb4
    0x2561,	// 0xb5
    0x2562,	// 0xb6
    0x2556,	// 0xb7
    0x2555,	// 0xb8
    0x2563,	// 0xb9
    0x2551,	// 0xba
    0x2557,	// 0xbb
    0x255d,	// 0xbc
    0x255c,	// 0xbd
    0x255b,	// 0xbe
    0x2510,	// 0xbf
    0x2514,	// 0xc0
    0x2534,	// 0xc1
    0x252c,	// 0xc2
    0x251c,	// 0xc3
    0x2500,	// 0xc4
    0x253c,	// 0xc5
    0x255e,	// 0xc6
    0x255f,	// 0xc7
    0x255a,	// 0xc8
    0x2554,	// 0xc9
    0x2569,	// 0xca
    0x2566,	// 0xcb
    0x2560,	// 0xcc
    0x2550,	// 0xcd
    0x256c,	// 0xce
    0x2567,	// 0xcf
    0x2568,	// 0xd0
    0x2564,	// 0xd1
    0x2565,	// 0xd2
    0x2559,	// 0xd3
    0x2558,	// 0xd4
    0x2552,	// 0xd5
    0x2553,	// 0xd6
    0x256b,	// 0xd7
    0x256a,	// 0xd8
    0x2518,	// 0xd9
    0x250c,	// 0xda
    0x2588,	// 0xdb
    0x2584,	// 0xdc
    0x258c,	// 0xdd
    0x2590,	// 0xde
    0x2580,	// 0xdf
    0x03b1,	// 0xe0
    0x00df,	// 0xe1
    0x0393,	// 0xe2
    0x03c0,	// 0xe3
    0x03a3,	// 0xe4
    0x03c3,	// 0xe5
    0x00b5,	// 0xe6
    0x03c4,	// 0xe7
    0x03a6,	// 0xe8
    0x0398,	// 0xe9
    0x03a9,	// 0xea
    0x03b4,	// 0xeb
    0x221e,	// 0xec
    0x03c6,	// 0xed
    0x03b5,	// 0xee
    0x2229,	// 0xef
    0x2261,	// 0xf0
    0x00b1,	// 0xf1
    0x2265,	// 0xf2
    0x2264,	// 0xf3
    0x2320,	// 0xf4
    0x2321,	// 0xf5
    0x00f7,	// 0xf6
    0x2248,	// 0xf7
    0x00b0,	// 0xf8
    0x2219,	// 0xf9
    0x00b7,	// 0xfa
    0x221a,	// 0xfb
    0x207f,	// 0xfc
    0x00b2,	// 0xfd
    0x25a0,	// 0xfe
    0x00a0	// 0xff
};

static const CharSetCvt::MapEnt to437[] = {
{0x00a0, 0xff},
{0x00a1, 0xad},
{0x00a2, 0x9b},
{0x00a3, 0x9c},
{0x00a5, 0x9d},
{0x00aa, 0xa6},
{0x00ab, 0xae},
{0x00ac, 0xaa},
{0x00b0, 0xf8},
{0x00b1, 0xf1},
{0x00b2, 0xfd},
{0x00b5, 0xe6},
{0x00b7, 0xfa},
{0x00ba, 0xa7},
{0x00bb, 0xaf},
{0x00bc, 0xac},
{0x00bd, 0xab},
{0x00bf, 0xa8},
{0x00c4, 0x8e},
{0x00c5, 0x8f},
{0x00c6, 0x92},
{0x00c7, 0x80},
{0x00c9, 0x90},
{0x00d1, 0xa5},
{0x00d6, 0x99},
{0x00dc, 0x9a},
{0x00df, 0xe1},
{0x00e0, 0x85},
{0x00e1, 0xa0},
{0x00e2, 0x83},
{0x00e4, 0x84},
{0x00e5, 0x86},
{0x00e6, 0x91},
{0x00e7, 0x87},
{0x00e8, 0x8a},
{0x00e9, 0x82},
{0x00ea, 0x88},
{0x00eb, 0x89},
{0x00ec, 0x8d},
{0x00ed, 0xa1},
{0x00ee, 0x8c},
{0x00ef, 0x8b},
{0x00f1, 0xa4},
{0x00f2, 0x95},
{0x00f3, 0xa2},
{0x00f4, 0x93},
{0x00f6, 0x94},
{0x00f7, 0xf6},
{0x00f9, 0x97},
{0x00fa, 0xa3},
{0x00fb, 0x96},
{0x00fc, 0x81},
{0x00ff, 0x98},
{0x0192, 0x9f},
{0x0393, 0xe2},
{0x0398, 0xe9},
{0x03a3, 0xe4},
{0x03a6, 0xe8},
{0x03a9, 0xea},
{0x03b1, 0xe0},
{0x03b4, 0xeb},
{0x03b5, 0xee},
{0x03c0, 0xe3},
{0x03c3, 0xe5},
{0x03c4, 0xe7},
{0x03c6, 0xed},
{0x207f, 0xfc},
{0x20a7, 0x9e},
{0x2219, 0xf9},
{0x221a, 0xfb},
{0x221e, 0xec},
{0x2229, 0xef},
{0x2248, 0xf7},
{0x2261, 0xf0},
{0x2264, 0xf3},
{0x2265, 0xf2},
{0x2310, 0xa9},
{0x2320, 0xf4},
{0x2321, 0xf5},
{0x2500, 0xc4},
{0x2502, 0xb3},
{0x250c, 0xda},
{0x2510, 0xbf},
{0x2514, 0xc0},
{0x2518, 0xd9},
{0x251c, 0xc3},
{0x2524, 0xb4},
{0x252c, 0xc2},
{0x2534, 0xc1},
{0x253c, 0xc5},
{0x2550, 0xcd},
{0x2551, 0xba},
{0x2552, 0xd5},
{0x2553, 0xd6},
{0x2554, 0xc9},
{0x2555, 0xb8},
{0x2556, 0xb7},
{0x2557, 0xbb},
{0x2558, 0xd4},
{0x2559, 0xd3},
{0x255a, 0xc8},
{0x255b, 0xbe},
{0x255c, 0xbd},
{0x255d, 0xbc},
{0x255e, 0xc6},
{0x255f, 0xc7},
{0x2560, 0xcc},
{0x2561, 0xb5},
{0x2562, 0xb6},
{0x2563, 0xb9},
{0x2564, 0xd1},
{0x2565, 0xd2},
{0x2566, 0xcb},
{0x2567, 0xcf},
{0x2568, 0xd0},
{0x2569, 0xca},
{0x256a, 0xd8},
{0x256b, 0xd7},
{0x256c, 0xce},
{0x2580, 0xdf},
{0x2584, 0xdc},
{0x2588, 0xdb},
{0x258c, 0xdd},
{0x2590, 0xde},
{0x2591, 0xb0},
{0x2592, 0xb1},
{0x2593, 0xb2},
{0x25a0, 0xfe}
};

static const unsigned short from850[] = {
    0x00c7, // 0x80 LATIN CAPITAL LETTER C WITH CEDILLA
    0x00fc, // 0x81 LATIN SMALL LETTER U WITH DIAERESIS
    0x00e9, // 0x82 LATIN SMALL LETTER E WITH ACUTE
    0x00e2, // 0x83 LATIN SMALL LETTER A WITH CIRCUMFLEX
    0x00e4, // 0x84 LATIN SMALL LETTER A WITH DIAERESIS
    0x00e0, // 0x85 LATIN SMALL LETTER A WITH GRAVE
    0x00e5, // 0x86 LATIN SMALL LETTER A WITH RING ABOVE
    0x00e7, // 0x87 LATIN SMALL LETTER C WITH CEDILLA
    0x00ea, // 0x88 LATIN SMALL LETTER E WITH CIRCUMFLEX
    0x00eb, // 0x89 LATIN SMALL LETTER E WITH DIAERESIS
    0x00e8, // 0x8a LATIN SMALL LETTER E WITH GRAVE
    0x00ef, // 0x8b LATIN SMALL LETTER I WITH DIAERESIS
    0x00ee, // 0x8c LATIN SMALL LETTER I WITH CIRCUMFLEX
    0x00ec, // 0x8d LATIN SMALL LETTER I WITH GRAVE
    0x00c4, // 0x8e LATIN CAPITAL LETTER A WITH DIAERESIS
    0x00c5, // 0x8f LATIN CAPITAL LETTER A WITH RING ABOVE
    0x00c9, // 0x90 LATIN CAPITAL LETTER E WITH ACUTE
    0x00e6, // 0x91 LATIN SMALL LIGATURE AE
    0x00c6, // 0x92 LATIN CAPITAL LIGATURE AE
    0x00f4, // 0x93 LATIN SMALL LETTER O WITH CIRCUMFLEX
    0x00f6, // 0x94 LATIN SMALL LETTER O WITH DIAERESIS
    0x00f2, // 0x95 LATIN SMALL LETTER O WITH GRAVE
    0x00fb, // 0x96 LATIN SMALL LETTER U WITH CIRCUMFLEX
    0x00f9, // 0x97 LATIN SMALL LETTER U WITH GRAVE
    0x00ff, // 0x98 LATIN SMALL LETTER Y WITH DIAERESIS
    0x00d6, // 0x99 LATIN CAPITAL LETTER O WITH DIAERESIS
    0x00dc, // 0x9a LATIN CAPITAL LETTER U WITH DIAERESIS
    0x00f8, // 0x9b LATIN SMALL LETTER O WITH STROKE
    0x00a3, // 0x9c POUND SIGN
    0x00d8, // 0x9d LATIN CAPITAL LETTER O WITH STROKE
    0x00d7, // 0x9e MULTIPLICATION SIGN
    0x0192, // 0x9f LATIN SMALL LETTER F WITH HOOK
    0x00e1, // 0xa0 LATIN SMALL LETTER A WITH ACUTE
    0x00ed, // 0xa1 LATIN SMALL LETTER I WITH ACUTE
    0x00f3, // 0xa2 LATIN SMALL LETTER O WITH ACUTE
    0x00fa, // 0xa3 LATIN SMALL LETTER U WITH ACUTE
    0x00f1, // 0xa4 LATIN SMALL LETTER N WITH TILDE
    0x00d1, // 0xa5 LATIN CAPITAL LETTER N WITH TILDE
    0x00aa, // 0xa6 FEMININE ORDINAL INDICATOR
    0x00ba, // 0xa7 MASCULINE ORDINAL INDICATOR
    0x00bf, // 0xa8 INVERTED QUESTION MARK
    0x00ae, // 0xa9 REGISTERED SIGN
    0x00ac, // 0xaa NOT SIGN
    0x00bd, // 0xab VULGAR FRACTION ONE HALF
    0x00bc, // 0xac VULGAR FRACTION ONE QUARTER
    0x00a1, // 0xad INVERTED EXCLAMATION MARK
    0x00ab, // 0xae LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00bb, // 0xaf RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x2591, // 0xb0 LIGHT SHADE
    0x2592, // 0xb1 MEDIUM SHADE
    0x2593, // 0xb2 DARK SHADE
    0x2502, // 0xb3 BOX DRAWINGS LIGHT VERTICAL
    0x2524, // 0xb4 BOX DRAWINGS LIGHT VERTICAL AND LEFT
    0x00c1, // 0xb5 LATIN CAPITAL LETTER A WITH ACUTE
    0x00c2, // 0xb6 LATIN CAPITAL LETTER A WITH CIRCUMFLEX
    0x00c0, // 0xb7 LATIN CAPITAL LETTER A WITH GRAVE
    0x00a9, // 0xb8 COPYRIGHT SIGN
    0x2563, // 0xb9 BOX DRAWINGS DOUBLE VERTICAL AND LEFT
    0x2551, // 0xba BOX DRAWINGS DOUBLE VERTICAL
    0x2557, // 0xbb BOX DRAWINGS DOUBLE DOWN AND LEFT
    0x255d, // 0xbc BOX DRAWINGS DOUBLE UP AND LEFT
    0x00a2, // 0xbd CENT SIGN
    0x00a5, // 0xbe YEN SIGN
    0x2510, // 0xbf BOX DRAWINGS LIGHT DOWN AND LEFT
    0x2514, // 0xc0 BOX DRAWINGS LIGHT UP AND RIGHT
    0x2534, // 0xc1 BOX DRAWINGS LIGHT UP AND HORIZONTAL
    0x252c, // 0xc2 BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
    0x251c, // 0xc3 BOX DRAWINGS LIGHT VERTICAL AND RIGHT
    0x2500, // 0xc4 BOX DRAWINGS LIGHT HORIZONTAL
    0x253c, // 0xc5 BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
    0x00e3, // 0xc6 LATIN SMALL LETTER A WITH TILDE
    0x00c3, // 0xc7 LATIN CAPITAL LETTER A WITH TILDE
    0x255a, // 0xc8 BOX DRAWINGS DOUBLE UP AND RIGHT
    0x2554, // 0xc9 BOX DRAWINGS DOUBLE DOWN AND RIGHT
    0x2569, // 0xca BOX DRAWINGS DOUBLE UP AND HORIZONTAL
    0x2566, // 0xcb BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
    0x2560, // 0xcc BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
    0x2550, // 0xcd BOX DRAWINGS DOUBLE HORIZONTAL
    0x256c, // 0xce BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
    0x00a4, // 0xcf CURRENCY SIGN
    0x00f0, // 0xd0 LATIN SMALL LETTER ETH
    0x00d0, // 0xd1 LATIN CAPITAL LETTER ETH
    0x00ca, // 0xd2 LATIN CAPITAL LETTER E WITH CIRCUMFLEX
    0x00cb, // 0xd3 LATIN CAPITAL LETTER E WITH DIAERESIS
    0x00c8, // 0xd4 LATIN CAPITAL LETTER E WITH GRAVE
    0x0131, // 0xd5 LATIN SMALL LETTER DOTLESS I
    0x00cd, // 0xd6 LATIN CAPITAL LETTER I WITH ACUTE
    0x00ce, // 0xd7 LATIN CAPITAL LETTER I WITH CIRCUMFLEX
    0x00cf, // 0xd8 LATIN CAPITAL LETTER I WITH DIAERESIS
    0x2518, // 0xd9 BOX DRAWINGS LIGHT UP AND LEFT
    0x250c, // 0xda BOX DRAWINGS LIGHT DOWN AND RIGHT
    0x2588, // 0xdb FULL BLOCK
    0x2584, // 0xdc LOWER HALF BLOCK
    0x00a6, // 0xdd BROKEN BAR
    0x00cc, // 0xde LATIN CAPITAL LETTER I WITH GRAVE
    0x2580, // 0xdf UPPER HALF BLOCK
    0x00d3, // 0xe0 LATIN CAPITAL LETTER O WITH ACUTE
    0x00df, // 0xe1 LATIN SMALL LETTER SHARP S
    0x00d4, // 0xe2 LATIN CAPITAL LETTER O WITH CIRCUMFLEX
    0x00d2, // 0xe3 LATIN CAPITAL LETTER O WITH GRAVE
    0x00f5, // 0xe4 LATIN SMALL LETTER O WITH TILDE
    0x00d5, // 0xe5 LATIN CAPITAL LETTER O WITH TILDE
    0x00b5, // 0xe6 MICRO SIGN
    0x00fe, // 0xe7 LATIN SMALL LETTER THORN
    0x00de, // 0xe8 LATIN CAPITAL LETTER THORN
    0x00da, // 0xe9 LATIN CAPITAL LETTER U WITH ACUTE
    0x00db, // 0xea LATIN CAPITAL LETTER U WITH CIRCUMFLEX
    0x00d9, // 0xeb LATIN CAPITAL LETTER U WITH GRAVE
    0x00fd, // 0xec LATIN SMALL LETTER Y WITH ACUTE
    0x00dd, // 0xed LATIN CAPITAL LETTER Y WITH ACUTE
    0x00af, // 0xee MACRON
    0x00b4, // 0xef ACUTE ACCENT
    0x00ad, // 0xf0 SOFT HYPHEN
    0x00b1, // 0xf1 PLUS-MINUS SIGN
    0x2017, // 0xf2 DOUBLE LOW LINE
    0x00be, // 0xf3 VULGAR FRACTION THREE QUARTERS
    0x00b6, // 0xf4 PILCROW SIGN
    0x00a7, // 0xf5 SECTION SIGN
    0x00f7, // 0xf6 DIVISION SIGN
    0x00b8, // 0xf7 CEDILLA
    0x00b0, // 0xf8 DEGREE SIGN
    0x00a8, // 0xf9 DIAERESIS
    0x00b7, // 0xfa MIDDLE DOT
    0x00b9, // 0xfb SUPERSCRIPT ONE
    0x00b3, // 0xfc SUPERSCRIPT THREE
    0x00b2, // 0xfd SUPERSCRIPT TWO
    0x25a0, // 0xfe BLACK SQUARE
    0x00a0  // 0xff NO-BREAK SPACE
};

static const CharSetCvt::MapEnt to850[] = {
{0x00a0, 0xff},	// NO-BREAK SPACE
{0x00a1, 0xad},	// INVERTED EXCLAMATION MARK
{0x00a2, 0xbd},	// CENT SIGN
{0x00a3, 0x9c},	// POUND SIGN
{0x00a4, 0xcf},	// CURRENCY SIGN
{0x00a5, 0xbe},	// YEN SIGN
{0x00a6, 0xdd},	// BROKEN BAR
{0x00a7, 0xf5},	// SECTION SIGN
{0x00a8, 0xf9},	// DIAERESIS
{0x00a9, 0xb8},	// COPYRIGHT SIGN
{0x00aa, 0xa6},	// FEMININE ORDINAL INDICATOR
{0x00ab, 0xae},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00ac, 0xaa},	// NOT SIGN
{0x00ad, 0xf0},	// SOFT HYPHEN
{0x00ae, 0xa9},	// REGISTERED SIGN
{0x00af, 0xee},	// MACRON
{0x00b0, 0xf8},	// DEGREE SIGN
{0x00b1, 0xf1},	// PLUS-MINUS SIGN
{0x00b2, 0xfd},	// SUPERSCRIPT TWO
{0x00b3, 0xfc},	// SUPERSCRIPT THREE
{0x00b4, 0xef},	// ACUTE ACCENT
{0x00b5, 0xe6},	// MICRO SIGN
{0x00b6, 0xf4},	// PILCROW SIGN
{0x00b7, 0xfa},	// MIDDLE DOT
{0x00b8, 0xf7},	// CEDILLA
{0x00b9, 0xfb},	// SUPERSCRIPT ONE
{0x00ba, 0xa7},	// MASCULINE ORDINAL INDICATOR
{0x00bb, 0xaf},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00bc, 0xac},	// VULGAR FRACTION ONE QUARTER
{0x00bd, 0xab},	// VULGAR FRACTION ONE HALF
{0x00be, 0xf3},	// VULGAR FRACTION THREE QUARTERS
{0x00bf, 0xa8},	// INVERTED QUESTION MARK
{0x00c0, 0xb7},	// LATIN CAPITAL LETTER A WITH GRAVE
{0x00c1, 0xb5},	// LATIN CAPITAL LETTER A WITH ACUTE
{0x00c2, 0xb6},	// LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00c3, 0xc7},	// LATIN CAPITAL LETTER A WITH TILDE
{0x00c4, 0x8e},	// LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00c5, 0x8f},	// LATIN CAPITAL LETTER A WITH RING ABOVE
{0x00c6, 0x92},	// LATIN CAPITAL LIGATURE AE
{0x00c7, 0x80},	// LATIN CAPITAL LETTER C WITH CEDILLA
{0x00c8, 0xd4},	// LATIN CAPITAL LETTER E WITH GRAVE
{0x00c9, 0x90},	// LATIN CAPITAL LETTER E WITH ACUTE
{0x00ca, 0xd2},	// LATIN CAPITAL LETTER E WITH CIRCUMFLEX
{0x00cb, 0xd3},	// LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00cc, 0xde},	// LATIN CAPITAL LETTER I WITH GRAVE
{0x00cd, 0xd6},	// LATIN CAPITAL LETTER I WITH ACUTE
{0x00ce, 0xd7},	// LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00cf, 0xd8},	// LATIN CAPITAL LETTER I WITH DIAERESIS
{0x00d0, 0xd1},	// LATIN CAPITAL LETTER ETH
{0x00d1, 0xa5},	// LATIN CAPITAL LETTER N WITH TILDE
{0x00d2, 0xe3},	// LATIN CAPITAL LETTER O WITH GRAVE
{0x00d3, 0xe0},	// LATIN CAPITAL LETTER O WITH ACUTE
{0x00d4, 0xe2},	// LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00d5, 0xe5},	// LATIN CAPITAL LETTER O WITH TILDE
{0x00d6, 0x99},	// LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00d7, 0x9e},	// MULTIPLICATION SIGN
{0x00d8, 0x9d},	// LATIN CAPITAL LETTER O WITH STROKE
{0x00d9, 0xeb},	// LATIN CAPITAL LETTER U WITH GRAVE
{0x00da, 0xe9},	// LATIN CAPITAL LETTER U WITH ACUTE
{0x00db, 0xea},	// LATIN CAPITAL LETTER U WITH CIRCUMFLEX
{0x00dc, 0x9a},	// LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00dd, 0xed},	// LATIN CAPITAL LETTER Y WITH ACUTE
{0x00de, 0xe8},	// LATIN CAPITAL LETTER THORN
{0x00df, 0xe1},	// LATIN SMALL LETTER SHARP S
{0x00e0, 0x85},	// LATIN SMALL LETTER A WITH GRAVE
{0x00e1, 0xa0},	// LATIN SMALL LETTER A WITH ACUTE
{0x00e2, 0x83},	// LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00e3, 0xc6},	// LATIN SMALL LETTER A WITH TILDE
{0x00e4, 0x84},	// LATIN SMALL LETTER A WITH DIAERESIS
{0x00e5, 0x86},	// LATIN SMALL LETTER A WITH RING ABOVE
{0x00e6, 0x91},	// LATIN SMALL LIGATURE AE
{0x00e7, 0x87},	// LATIN SMALL LETTER C WITH CEDILLA
{0x00e8, 0x8a},	// LATIN SMALL LETTER E WITH GRAVE
{0x00e9, 0x82},	// LATIN SMALL LETTER E WITH ACUTE
{0x00ea, 0x88},	// LATIN SMALL LETTER E WITH CIRCUMFLEX
{0x00eb, 0x89},	// LATIN SMALL LETTER E WITH DIAERESIS
{0x00ec, 0x8d},	// LATIN SMALL LETTER I WITH GRAVE
{0x00ed, 0xa1},	// LATIN SMALL LETTER I WITH ACUTE
{0x00ee, 0x8c},	// LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00ef, 0x8b},	// LATIN SMALL LETTER I WITH DIAERESIS
{0x00f0, 0xd0},	// LATIN SMALL LETTER ETH
{0x00f1, 0xa4},	// LATIN SMALL LETTER N WITH TILDE
{0x00f2, 0x95},	// LATIN SMALL LETTER O WITH GRAVE
{0x00f3, 0xa2},	// LATIN SMALL LETTER O WITH ACUTE
{0x00f4, 0x93},	// LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00f5, 0xe4},	// LATIN SMALL LETTER O WITH TILDE
{0x00f6, 0x94},	// LATIN SMALL LETTER O WITH DIAERESIS
{0x00f7, 0xf6},	// DIVISION SIGN
{0x00f8, 0x9b},	// LATIN SMALL LETTER O WITH STROKE
{0x00f9, 0x97},	// LATIN SMALL LETTER U WITH GRAVE
{0x00fa, 0xa3},	// LATIN SMALL LETTER U WITH ACUTE
{0x00fb, 0x96},	// LATIN SMALL LETTER U WITH CIRCUMFLEX
{0x00fc, 0x81},	// LATIN SMALL LETTER U WITH DIAERESIS
{0x00fd, 0xec},	// LATIN SMALL LETTER Y WITH ACUTE
{0x00fe, 0xe7},	// LATIN SMALL LETTER THORN
{0x00ff, 0x98},	// LATIN SMALL LETTER Y WITH DIAERESIS
{0x0131, 0xd5},	// LATIN SMALL LETTER DOTLESS I
{0x0192, 0x9f},	// LATIN SMALL LETTER F WITH HOOK
{0x2017, 0xf2},	// DOUBLE LOW LINE
{0x2500, 0xc4},	// BOX DRAWINGS LIGHT HORIZONTAL
{0x2502, 0xb3},	// BOX DRAWINGS LIGHT VERTICAL
{0x250c, 0xda},	// BOX DRAWINGS LIGHT DOWN AND RIGHT
{0x2510, 0xbf},	// BOX DRAWINGS LIGHT DOWN AND LEFT
{0x2514, 0xc0},	// BOX DRAWINGS LIGHT UP AND RIGHT
{0x2518, 0xd9},	// BOX DRAWINGS LIGHT UP AND LEFT
{0x251c, 0xc3},	// BOX DRAWINGS LIGHT VERTICAL AND RIGHT
{0x2524, 0xb4},	// BOX DRAWINGS LIGHT VERTICAL AND LEFT
{0x252c, 0xc2},	// BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
{0x2534, 0xc1},	// BOX DRAWINGS LIGHT UP AND HORIZONTAL
{0x253c, 0xc5},	// BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
{0x2550, 0xcd},	// BOX DRAWINGS DOUBLE HORIZONTAL
{0x2551, 0xba},	// BOX DRAWINGS DOUBLE VERTICAL
{0x2554, 0xc9},	// BOX DRAWINGS DOUBLE DOWN AND RIGHT
{0x2557, 0xbb},	// BOX DRAWINGS DOUBLE DOWN AND LEFT
{0x255a, 0xc8},	// BOX DRAWINGS DOUBLE UP AND RIGHT
{0x255d, 0xbc},	// BOX DRAWINGS DOUBLE UP AND LEFT
{0x2560, 0xcc},	// BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
{0x2563, 0xb9},	// BOX DRAWINGS DOUBLE VERTICAL AND LEFT
{0x2566, 0xcb},	// BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
{0x2569, 0xca},	// BOX DRAWINGS DOUBLE UP AND HORIZONTAL
{0x256c, 0xce},	// BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
{0x2580, 0xdf},	// UPPER HALF BLOCK
{0x2584, 0xdc},	// LOWER HALF BLOCK
{0x2588, 0xdb},	// FULL BLOCK
{0x2591, 0xb0},	// LIGHT SHADE
{0x2592, 0xb1},	// MEDIUM SHADE
{0x2593, 0xb2},	// DARK SHADE
{0x25a0, 0xfe}	// BLACK SQUARE
};

static const unsigned short from858[] = {
    0x00c7, // 0x80 LATIN CAPITAL LETTER C WITH CEDILLA
    0x00fc, // 0x81 LATIN SMALL LETTER U WITH DIAERESIS
    0x00e9, // 0x82 LATIN SMALL LETTER E WITH ACUTE
    0x00e2, // 0x83 LATIN SMALL LETTER A WITH CIRCUMFLEX
    0x00e4, // 0x84 LATIN SMALL LETTER A WITH DIAERESIS
    0x00e0, // 0x85 LATIN SMALL LETTER A WITH GRAVE
    0x00e5, // 0x86 LATIN SMALL LETTER A WITH RING ABOVE
    0x00e7, // 0x87 LATIN SMALL LETTER C WITH CEDILLA
    0x00ea, // 0x88 LATIN SMALL LETTER E WITH CIRCUMFLEX
    0x00eb, // 0x89 LATIN SMALL LETTER E WITH DIAERESIS
    0x00e8, // 0x8a LATIN SMALL LETTER E WITH GRAVE
    0x00ef, // 0x8b LATIN SMALL LETTER I WITH DIAERESIS
    0x00ee, // 0x8c LATIN SMALL LETTER I WITH CIRCUMFLEX
    0x00ec, // 0x8d LATIN SMALL LETTER I WITH GRAVE
    0x00c4, // 0x8e LATIN CAPITAL LETTER A WITH DIAERESIS
    0x00c5, // 0x8f LATIN CAPITAL LETTER A WITH RING ABOVE
    0x00c9, // 0x90 LATIN CAPITAL LETTER E WITH ACUTE
    0x00e6, // 0x91 LATIN SMALL LIGATURE AE
    0x00c6, // 0x92 LATIN CAPITAL LIGATURE AE
    0x00f4, // 0x93 LATIN SMALL LETTER O WITH CIRCUMFLEX
    0x00f6, // 0x94 LATIN SMALL LETTER O WITH DIAERESIS
    0x00f2, // 0x95 LATIN SMALL LETTER O WITH GRAVE
    0x00fb, // 0x96 LATIN SMALL LETTER U WITH CIRCUMFLEX
    0x00f9, // 0x97 LATIN SMALL LETTER U WITH GRAVE
    0x00ff, // 0x98 LATIN SMALL LETTER Y WITH DIAERESIS
    0x00d6, // 0x99 LATIN CAPITAL LETTER O WITH DIAERESIS
    0x00dc, // 0x9a LATIN CAPITAL LETTER U WITH DIAERESIS
    0x00f8, // 0x9b LATIN SMALL LETTER O WITH STROKE
    0x00a3, // 0x9c POUND SIGN
    0x00d8, // 0x9d LATIN CAPITAL LETTER O WITH STROKE
    0x00d7, // 0x9e MULTIPLICATION SIGN
    0x0192, // 0x9f LATIN SMALL LETTER F WITH HOOK
    0x00e1, // 0xa0 LATIN SMALL LETTER A WITH ACUTE
    0x00ed, // 0xa1 LATIN SMALL LETTER I WITH ACUTE
    0x00f3, // 0xa2 LATIN SMALL LETTER O WITH ACUTE
    0x00fa, // 0xa3 LATIN SMALL LETTER U WITH ACUTE
    0x00f1, // 0xa4 LATIN SMALL LETTER N WITH TILDE
    0x00d1, // 0xa5 LATIN CAPITAL LETTER N WITH TILDE
    0x00aa, // 0xa6 FEMININE ORDINAL INDICATOR
    0x00ba, // 0xa7 MASCULINE ORDINAL INDICATOR
    0x00bf, // 0xa8 INVERTED QUESTION MARK
    0x00ae, // 0xa9 REGISTERED SIGN
    0x00ac, // 0xaa NOT SIGN
    0x00bd, // 0xab VULGAR FRACTION ONE HALF
    0x00bc, // 0xac VULGAR FRACTION ONE QUARTER
    0x00a1, // 0xad INVERTED EXCLAMATION MARK
    0x00ab, // 0xae LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00bb, // 0xaf RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x2591, // 0xb0 LIGHT SHADE
    0x2592, // 0xb1 MEDIUM SHADE
    0x2593, // 0xb2 DARK SHADE
    0x2502, // 0xb3 BOX DRAWINGS LIGHT VERTICAL
    0x2524, // 0xb4 BOX DRAWINGS LIGHT VERTICAL AND LEFT
    0x00c1, // 0xb5 LATIN CAPITAL LETTER A WITH ACUTE
    0x00c2, // 0xb6 LATIN CAPITAL LETTER A WITH CIRCUMFLEX
    0x00c0, // 0xb7 LATIN CAPITAL LETTER A WITH GRAVE
    0x00a9, // 0xb8 COPYRIGHT SIGN
    0x2563, // 0xb9 BOX DRAWINGS DOUBLE VERTICAL AND LEFT
    0x2551, // 0xba BOX DRAWINGS DOUBLE VERTICAL
    0x2557, // 0xbb BOX DRAWINGS DOUBLE DOWN AND LEFT
    0x255d, // 0xbc BOX DRAWINGS DOUBLE UP AND LEFT
    0x00a2, // 0xbd CENT SIGN
    0x00a5, // 0xbe YEN SIGN
    0x2510, // 0xbf BOX DRAWINGS LIGHT DOWN AND LEFT
    0x2514, // 0xc0 BOX DRAWINGS LIGHT UP AND RIGHT
    0x2534, // 0xc1 BOX DRAWINGS LIGHT UP AND HORIZONTAL
    0x252c, // 0xc2 BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
    0x251c, // 0xc3 BOX DRAWINGS LIGHT VERTICAL AND RIGHT
    0x2500, // 0xc4 BOX DRAWINGS LIGHT HORIZONTAL
    0x253c, // 0xc5 BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
    0x00e3, // 0xc6 LATIN SMALL LETTER A WITH TILDE
    0x00c3, // 0xc7 LATIN CAPITAL LETTER A WITH TILDE
    0x255a, // 0xc8 BOX DRAWINGS DOUBLE UP AND RIGHT
    0x2554, // 0xc9 BOX DRAWINGS DOUBLE DOWN AND RIGHT
    0x2569, // 0xca BOX DRAWINGS DOUBLE UP AND HORIZONTAL
    0x2566, // 0xcb BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
    0x2560, // 0xcc BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
    0x2550, // 0xcd BOX DRAWINGS DOUBLE HORIZONTAL
    0x256c, // 0xce BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
    0x00a4, // 0xcf CURRENCY SIGN
    0x00f0, // 0xd0 LATIN SMALL LETTER ETH
    0x00d0, // 0xd1 LATIN CAPITAL LETTER ETH
    0x00ca, // 0xd2 LATIN CAPITAL LETTER E WITH CIRCUMFLEX
    0x00cb, // 0xd3 LATIN CAPITAL LETTER E WITH DIAERESIS
    0x00c8, // 0xd4 LATIN CAPITAL LETTER E WITH GRAVE
    0x20ac, // 0xd5 EURO SIGN
    0x00cd, // 0xd6 LATIN CAPITAL LETTER I WITH ACUTE
    0x00ce, // 0xd7 LATIN CAPITAL LETTER I WITH CIRCUMFLEX
    0x00cf, // 0xd8 LATIN CAPITAL LETTER I WITH DIAERESIS
    0x2518, // 0xd9 BOX DRAWINGS LIGHT UP AND LEFT
    0x250c, // 0xda BOX DRAWINGS LIGHT DOWN AND RIGHT
    0x2588, // 0xdb FULL BLOCK
    0x2584, // 0xdc LOWER HALF BLOCK
    0x00a6, // 0xdd BROKEN BAR
    0x00cc, // 0xde LATIN CAPITAL LETTER I WITH GRAVE
    0x2580, // 0xdf UPPER HALF BLOCK
    0x00d3, // 0xe0 LATIN CAPITAL LETTER O WITH ACUTE
    0x00df, // 0xe1 LATIN SMALL LETTER SHARP S
    0x00d4, // 0xe2 LATIN CAPITAL LETTER O WITH CIRCUMFLEX
    0x00d2, // 0xe3 LATIN CAPITAL LETTER O WITH GRAVE
    0x00f5, // 0xe4 LATIN SMALL LETTER O WITH TILDE
    0x00d5, // 0xe5 LATIN CAPITAL LETTER O WITH TILDE
    0x00b5, // 0xe6 MICRO SIGN
    0x00fe, // 0xe7 LATIN SMALL LETTER THORN
    0x00de, // 0xe8 LATIN CAPITAL LETTER THORN
    0x00da, // 0xe9 LATIN CAPITAL LETTER U WITH ACUTE
    0x00db, // 0xea LATIN CAPITAL LETTER U WITH CIRCUMFLEX
    0x00d9, // 0xeb LATIN CAPITAL LETTER U WITH GRAVE
    0x00fd, // 0xec LATIN SMALL LETTER Y WITH ACUTE
    0x00dd, // 0xed LATIN CAPITAL LETTER Y WITH ACUTE
    0x00af, // 0xee MACRON
    0x00b4, // 0xef ACUTE ACCENT
    0x00ad, // 0xf0 SOFT HYPHEN
    0x00b1, // 0xf1 PLUS-MINUS SIGN
    0x2017, // 0xf2 DOUBLE LOW LINE
    0x00be, // 0xf3 VULGAR FRACTION THREE QUARTERS
    0x00b6, // 0xf4 PILCROW SIGN
    0x00a7, // 0xf5 SECTION SIGN
    0x00f7, // 0xf6 DIVISION SIGN
    0x00b8, // 0xf7 CEDILLA
    0x00b0, // 0xf8 DEGREE SIGN
    0x00a8, // 0xf9 DIAERESIS
    0x00b7, // 0xfa MIDDLE DOT
    0x00b9, // 0xfb SUPERSCRIPT ONE
    0x00b3, // 0xfc SUPERSCRIPT THREE
    0x00b2, // 0xfd SUPERSCRIPT TWO
    0x25a0, // 0xfe BLACK SQUARE
    0x00a0  // 0xff NO-BREAK SPACE
};

static const CharSetCvt::MapEnt to858[] = {
{0x00a0, 0xff},	// NO-BREAK SPACE
{0x00a1, 0xad},	// INVERTED EXCLAMATION MARK
{0x00a2, 0xbd},	// CENT SIGN
{0x00a3, 0x9c},	// POUND SIGN
{0x00a4, 0xcf},	// CURRENCY SIGN
{0x00a5, 0xbe},	// YEN SIGN
{0x00a6, 0xdd},	// BROKEN BAR
{0x00a7, 0xf5},	// SECTION SIGN
{0x00a8, 0xf9},	// DIAERESIS
{0x00a9, 0xb8},	// COPYRIGHT SIGN
{0x00aa, 0xa6},	// FEMININE ORDINAL INDICATOR
{0x00ab, 0xae},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00ac, 0xaa},	// NOT SIGN
{0x00ad, 0xf0},	// SOFT HYPHEN
{0x00ae, 0xa9},	// REGISTERED SIGN
{0x00af, 0xee},	// MACRON
{0x00b0, 0xf8},	// DEGREE SIGN
{0x00b1, 0xf1},	// PLUS-MINUS SIGN
{0x00b2, 0xfd},	// SUPERSCRIPT TWO
{0x00b3, 0xfc},	// SUPERSCRIPT THREE
{0x00b4, 0xef},	// ACUTE ACCENT
{0x00b5, 0xe6},	// MICRO SIGN
{0x00b6, 0xf4},	// PILCROW SIGN
{0x00b7, 0xfa},	// MIDDLE DOT
{0x00b8, 0xf7},	// CEDILLA
{0x00b9, 0xfb},	// SUPERSCRIPT ONE
{0x00ba, 0xa7},	// MASCULINE ORDINAL INDICATOR
{0x00bb, 0xaf},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00bc, 0xac},	// VULGAR FRACTION ONE QUARTER
{0x00bd, 0xab},	// VULGAR FRACTION ONE HALF
{0x00be, 0xf3},	// VULGAR FRACTION THREE QUARTERS
{0x00bf, 0xa8},	// INVERTED QUESTION MARK
{0x00c0, 0xb7},	// LATIN CAPITAL LETTER A WITH GRAVE
{0x00c1, 0xb5},	// LATIN CAPITAL LETTER A WITH ACUTE
{0x00c2, 0xb6},	// LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00c3, 0xc7},	// LATIN CAPITAL LETTER A WITH TILDE
{0x00c4, 0x8e},	// LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00c5, 0x8f},	// LATIN CAPITAL LETTER A WITH RING ABOVE
{0x00c6, 0x92},	// LATIN CAPITAL LIGATURE AE
{0x00c7, 0x80},	// LATIN CAPITAL LETTER C WITH CEDILLA
{0x00c8, 0xd4},	// LATIN CAPITAL LETTER E WITH GRAVE
{0x00c9, 0x90},	// LATIN CAPITAL LETTER E WITH ACUTE
{0x00ca, 0xd2},	// LATIN CAPITAL LETTER E WITH CIRCUMFLEX
{0x00cb, 0xd3},	// LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00cc, 0xde},	// LATIN CAPITAL LETTER I WITH GRAVE
{0x00cd, 0xd6},	// LATIN CAPITAL LETTER I WITH ACUTE
{0x00ce, 0xd7},	// LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00cf, 0xd8},	// LATIN CAPITAL LETTER I WITH DIAERESIS
{0x00d0, 0xd1},	// LATIN CAPITAL LETTER ETH
{0x00d1, 0xa5},	// LATIN CAPITAL LETTER N WITH TILDE
{0x00d2, 0xe3},	// LATIN CAPITAL LETTER O WITH GRAVE
{0x00d3, 0xe0},	// LATIN CAPITAL LETTER O WITH ACUTE
{0x00d4, 0xe2},	// LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00d5, 0xe5},	// LATIN CAPITAL LETTER O WITH TILDE
{0x00d6, 0x99},	// LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00d7, 0x9e},	// MULTIPLICATION SIGN
{0x00d8, 0x9d},	// LATIN CAPITAL LETTER O WITH STROKE
{0x00d9, 0xeb},	// LATIN CAPITAL LETTER U WITH GRAVE
{0x00da, 0xe9},	// LATIN CAPITAL LETTER U WITH ACUTE
{0x00db, 0xea},	// LATIN CAPITAL LETTER U WITH CIRCUMFLEX
{0x00dc, 0x9a},	// LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00dd, 0xed},	// LATIN CAPITAL LETTER Y WITH ACUTE
{0x00de, 0xe8},	// LATIN CAPITAL LETTER THORN
{0x00df, 0xe1},	// LATIN SMALL LETTER SHARP S
{0x00e0, 0x85},	// LATIN SMALL LETTER A WITH GRAVE
{0x00e1, 0xa0},	// LATIN SMALL LETTER A WITH ACUTE
{0x00e2, 0x83},	// LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00e3, 0xc6},	// LATIN SMALL LETTER A WITH TILDE
{0x00e4, 0x84},	// LATIN SMALL LETTER A WITH DIAERESIS
{0x00e5, 0x86},	// LATIN SMALL LETTER A WITH RING ABOVE
{0x00e6, 0x91},	// LATIN SMALL LIGATURE AE
{0x00e7, 0x87},	// LATIN SMALL LETTER C WITH CEDILLA
{0x00e8, 0x8a},	// LATIN SMALL LETTER E WITH GRAVE
{0x00e9, 0x82},	// LATIN SMALL LETTER E WITH ACUTE
{0x00ea, 0x88},	// LATIN SMALL LETTER E WITH CIRCUMFLEX
{0x00eb, 0x89},	// LATIN SMALL LETTER E WITH DIAERESIS
{0x00ec, 0x8d},	// LATIN SMALL LETTER I WITH GRAVE
{0x00ed, 0xa1},	// LATIN SMALL LETTER I WITH ACUTE
{0x00ee, 0x8c},	// LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00ef, 0x8b},	// LATIN SMALL LETTER I WITH DIAERESIS
{0x00f0, 0xd0},	// LATIN SMALL LETTER ETH
{0x00f1, 0xa4},	// LATIN SMALL LETTER N WITH TILDE
{0x00f2, 0x95},	// LATIN SMALL LETTER O WITH GRAVE
{0x00f3, 0xa2},	// LATIN SMALL LETTER O WITH ACUTE
{0x00f4, 0x93},	// LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00f5, 0xe4},	// LATIN SMALL LETTER O WITH TILDE
{0x00f6, 0x94},	// LATIN SMALL LETTER O WITH DIAERESIS
{0x00f7, 0xf6},	// DIVISION SIGN
{0x00f8, 0x9b},	// LATIN SMALL LETTER O WITH STROKE
{0x00f9, 0x97},	// LATIN SMALL LETTER U WITH GRAVE
{0x00fa, 0xa3},	// LATIN SMALL LETTER U WITH ACUTE
{0x00fb, 0x96},	// LATIN SMALL LETTER U WITH CIRCUMFLEX
{0x00fc, 0x81},	// LATIN SMALL LETTER U WITH DIAERESIS
{0x00fd, 0xec},	// LATIN SMALL LETTER Y WITH ACUTE
{0x00fe, 0xe7},	// LATIN SMALL LETTER THORN
{0x00ff, 0x98},	// LATIN SMALL LETTER Y WITH DIAERESIS
{0x0192, 0x9f},	// LATIN SMALL LETTER F WITH HOOK
{0x2017, 0xf2},	// DOUBLE LOW LINE
{0x20ac, 0xd5},	// EURO SIGN
{0x2500, 0xc4},	// BOX DRAWINGS LIGHT HORIZONTAL
{0x2502, 0xb3},	// BOX DRAWINGS LIGHT VERTICAL
{0x250c, 0xda},	// BOX DRAWINGS LIGHT DOWN AND RIGHT
{0x2510, 0xbf},	// BOX DRAWINGS LIGHT DOWN AND LEFT
{0x2514, 0xc0},	// BOX DRAWINGS LIGHT UP AND RIGHT
{0x2518, 0xd9},	// BOX DRAWINGS LIGHT UP AND LEFT
{0x251c, 0xc3},	// BOX DRAWINGS LIGHT VERTICAL AND RIGHT
{0x2524, 0xb4},	// BOX DRAWINGS LIGHT VERTICAL AND LEFT
{0x252c, 0xc2},	// BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
{0x2534, 0xc1},	// BOX DRAWINGS LIGHT UP AND HORIZONTAL
{0x253c, 0xc5},	// BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
{0x2550, 0xcd},	// BOX DRAWINGS DOUBLE HORIZONTAL
{0x2551, 0xba},	// BOX DRAWINGS DOUBLE VERTICAL
{0x2554, 0xc9},	// BOX DRAWINGS DOUBLE DOWN AND RIGHT
{0x2557, 0xbb},	// BOX DRAWINGS DOUBLE DOWN AND LEFT
{0x255a, 0xc8},	// BOX DRAWINGS DOUBLE UP AND RIGHT
{0x255d, 0xbc},	// BOX DRAWINGS DOUBLE UP AND LEFT
{0x2560, 0xcc},	// BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
{0x2563, 0xb9},	// BOX DRAWINGS DOUBLE VERTICAL AND LEFT
{0x2566, 0xcb},	// BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
{0x2569, 0xca},	// BOX DRAWINGS DOUBLE UP AND HORIZONTAL
{0x256c, 0xce},	// BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
{0x2580, 0xdf},	// UPPER HALF BLOCK
{0x2584, 0xdc},	// LOWER HALF BLOCK
{0x2588, 0xdb},	// FULL BLOCK
{0x2591, 0xb0},	// LIGHT SHADE
{0x2592, 0xb1},	// MEDIUM SHADE
{0x2593, 0xb2},	// DARK SHADE
{0x25a0, 0xfe}	// BLACK SQUARE
};

static const unsigned short fromMACROMAN[] = {
    0x00C4,	// 0x80 LATIN CAPITAL LETTER A WITH DIAERESI
    0x00C5,	// 0x81 LATIN CAPITAL LETTER A WITH RING ABOV
    0x00C7,	// 0x82 LATIN CAPITAL LETTER C WITH CEDILL
    0x00C9,	// 0x83 LATIN CAPITAL LETTER E WITH ACUT
    0x00D1,	// 0x84 LATIN CAPITAL LETTER N WITH TILD
    0x00D6,	// 0x85 LATIN CAPITAL LETTER O WITH DIAERESI
    0x00DC,	// 0x86 LATIN CAPITAL LETTER U WITH DIAERESI
    0x00E1,	// 0x87 LATIN SMALL LETTER A WITH ACUT
    0x00E0,	// 0x88 LATIN SMALL LETTER A WITH GRAV
    0x00E2,	// 0x89 LATIN SMALL LETTER A WITH CIRCUMFLE
    0x00E4,	// 0x8A LATIN SMALL LETTER A WITH DIAERESI
    0x00E3,	// 0x8B LATIN SMALL LETTER A WITH TILD
    0x00E5,	// 0x8C LATIN SMALL LETTER A WITH RING ABOV
    0x00E7,	// 0x8D LATIN SMALL LETTER C WITH CEDILL
    0x00E9,	// 0x8E LATIN SMALL LETTER E WITH ACUT
    0x00E8,	// 0x8F LATIN SMALL LETTER E WITH GRAV
    0x00EA,	// 0x90 LATIN SMALL LETTER E WITH CIRCUMFLE
    0x00EB,	// 0x91 LATIN SMALL LETTER E WITH DIAERESI
    0x00ED,	// 0x92 LATIN SMALL LETTER I WITH ACUT
    0x00EC,	// 0x93 LATIN SMALL LETTER I WITH GRAV
    0x00EE,	// 0x94 LATIN SMALL LETTER I WITH CIRCUMFLE
    0x00EF,	// 0x95 LATIN SMALL LETTER I WITH DIAERESI
    0x00F1,	// 0x96 LATIN SMALL LETTER N WITH TILD
    0x00F3,	// 0x97 LATIN SMALL LETTER O WITH ACUT
    0x00F2,	// 0x98 LATIN SMALL LETTER O WITH GRAV
    0x00F4,	// 0x99 LATIN SMALL LETTER O WITH CIRCUMFLE
    0x00F6,	// 0x9A LATIN SMALL LETTER O WITH DIAERESI
    0x00F5,	// 0x9B LATIN SMALL LETTER O WITH TILD
    0x00FA,	// 0x9C LATIN SMALL LETTER U WITH ACUT
    0x00F9,	// 0x9D LATIN SMALL LETTER U WITH GRAV
    0x00FB,	// 0x9E LATIN SMALL LETTER U WITH CIRCUMFLE
    0x00FC,	// 0x9F LATIN SMALL LETTER U WITH DIAERESI
    0x2020,	// 0xA0 DAGGE
    0x00B0,	// 0xA1 DEGREE SIG
    0x00A2,	// 0xA2 CENT SIG
    0x00A3,	// 0xA3 POUND SIG
    0x00A7,	// 0xA4 SECTION SIG
    0x2022,	// 0xA5 BULLE
    0x00B6,	// 0xA6 PILCROW SIG
    0x00DF,	// 0xA7 LATIN SMALL LETTER SHARP 
    0x00AE,	// 0xA8 REGISTERED SIG
    0x00A9,	// 0xA9 COPYRIGHT SIG
    0x2122,	// 0xAA TRADE MARK SIG
    0x00B4,	// 0xAB ACUTE ACCEN
    0x00A8,	// 0xAC DIAERESI
    0x2260,	// 0xAD NOT EQUAL T
    0x00C6,	// 0xAE LATIN CAPITAL LETTER A
    0x00D8,	// 0xAF LATIN CAPITAL LETTER O WITH STROK
    0x221E,	// 0xB0 INFINIT
    0x00B1,	// 0xB1 PLUS-MINUS SIG
    0x2264,	// 0xB2 LESS-THAN OR EQUAL T
    0x2265,	// 0xB3 GREATER-THAN OR EQUAL T
    0x00A5,	// 0xB4 YEN SIG
    0x00B5,	// 0xB5 MICRO SIG
    0x2202,	// 0xB6 PARTIAL DIFFERENTIA
    0x2211,	// 0xB7 N-ARY SUMMATIO
    0x220F,	// 0xB8 N-ARY PRODUC
    0x03C0,	// 0xB9 GREEK SMALL LETTER P
    0x222B,	// 0xBA INTEGRA
    0x00AA,	// 0xBB FEMININE ORDINAL INDICATO
    0x00BA,	// 0xBC MASCULINE ORDINAL INDICATO
    0x03A9,	// 0xBD GREEK CAPITAL LETTER OMEG
    0x00E6,	// 0xBE LATIN SMALL LETTER A
    0x00F8,	// 0xBF LATIN SMALL LETTER O WITH STROK
    0x00BF,	// 0xC0 INVERTED QUESTION MAR
    0x00A1,	// 0xC1 INVERTED EXCLAMATION MAR
    0x00AC,	// 0xC2 NOT SIG
    0x221A,	// 0xC3 SQUARE ROO
    0x0192,	// 0xC4 LATIN SMALL LETTER F WITH HOO
    0x2248,	// 0xC5 ALMOST EQUAL T
    0x2206,	// 0xC6 INCREMEN
    0x00AB,	// 0xC7 LEFT-POINTING DOUBLE ANGLE QUOTATION MAR
    0x00BB,	// 0xC8 RIGHT-POINTING DOUBLE ANGLE QUOTATION MAR
    0x2026,	// 0xC9 HORIZONTAL ELLIPSI
    0x00A0,	// 0xCA NO-BREAK SPAC
    0x00C0,	// 0xCB LATIN CAPITAL LETTER A WITH GRAV
    0x00C3,	// 0xCC LATIN CAPITAL LETTER A WITH TILD
    0x00D5,	// 0xCD LATIN CAPITAL LETTER O WITH TILD
    0x0152,	// 0xCE LATIN CAPITAL LIGATURE O
    0x0153,	// 0xCF LATIN SMALL LIGATURE O
    0x2013,	// 0xD0 EN DAS
    0x2014,	// 0xD1 EM DAS
    0x201C,	// 0xD2 LEFT DOUBLE QUOTATION MAR
    0x201D,	// 0xD3 RIGHT DOUBLE QUOTATION MAR
    0x2018,	// 0xD4 LEFT SINGLE QUOTATION MAR
    0x2019,	// 0xD5 RIGHT SINGLE QUOTATION MAR
    0x00F7,	// 0xD6 DIVISION SIG
    0x25CA,	// 0xD7 LOZENG
    0x00FF,	// 0xD8 LATIN SMALL LETTER Y WITH DIAERESI
    0x0178,	// 0xD9 LATIN CAPITAL LETTER Y WITH DIAERESI
    0x2044,	// 0xDA FRACTION SLAS
    0x20AC,	// 0xDB EURO SIG
    0x2039,	// 0xDC SINGLE LEFT-POINTING ANGLE QUOTATION MAR
    0x203A,	// 0xDD SINGLE RIGHT-POINTING ANGLE QUOTATION MAR
    0xFB01,	// 0xDE LATIN SMALL LIGATURE F
    0xFB02,	// 0xDF LATIN SMALL LIGATURE F
    0x2021,	// 0xE0 DOUBLE DAGGE
    0x00B7,	// 0xE1 MIDDLE DO
    0x201A,	// 0xE2 SINGLE LOW-9 QUOTATION MAR
    0x201E,	// 0xE3 DOUBLE LOW-9 QUOTATION MAR
    0x2030,	// 0xE4 PER MILLE SIG
    0x00C2,	// 0xE5 LATIN CAPITAL LETTER A WITH CIRCUMFLE
    0x00CA,	// 0xE6 LATIN CAPITAL LETTER E WITH CIRCUMFLE
    0x00C1,	// 0xE7 LATIN CAPITAL LETTER A WITH ACUT
    0x00CB,	// 0xE8 LATIN CAPITAL LETTER E WITH DIAERESI
    0x00C8,	// 0xE9 LATIN CAPITAL LETTER E WITH GRAV
    0x00CD,	// 0xEA LATIN CAPITAL LETTER I WITH ACUT
    0x00CE,	// 0xEB LATIN CAPITAL LETTER I WITH CIRCUMFLE
    0x00CF,	// 0xEC LATIN CAPITAL LETTER I WITH DIAERESI
    0x00CC,	// 0xED LATIN CAPITAL LETTER I WITH GRAV
    0x00D3,	// 0xEE LATIN CAPITAL LETTER O WITH ACUT
    0x00D4,	// 0xEF LATIN CAPITAL LETTER O WITH CIRCUMFLE
    0xF8FF,	// 0xF0 Apple logo (copyrighted
    0x00D2,	// 0xF1 LATIN CAPITAL LETTER O WITH GRAV
    0x00DA,	// 0xF2 LATIN CAPITAL LETTER U WITH ACUT
    0x00DB,	// 0xF3 LATIN CAPITAL LETTER U WITH CIRCUMFLE
    0x00D9,	// 0xF4 LATIN CAPITAL LETTER U WITH GRAV
    0x0131,	// 0xF5 LATIN SMALL LETTER DOTLESS 
    0x02C6,	// 0xF6 MODIFIER LETTER CIRCUMFLEX ACCEN
    0x02DC,	// 0xF7 SMALL TILD
    0x00AF,	// 0xF8 MACRO
    0x02D8,	// 0xF9 BREV
    0x02D9,	// 0xFA DOT ABOV
    0x02DA,	// 0xFB RING ABOV
    0x00B8,	// 0xFC CEDILL
    0x02DD,	// 0xFD DOUBLE ACUTE ACCEN
    0x02DB,	// 0xFE OGONE
    0x02C7	// 0xFF CARON
};

static const CharSetCvt::MapEnt toMACROMAN[] = {
{0x00A0, 0xCA},	// NO-BREAK SPACE
{0x00A1, 0xC1},	// INVERTED EXCLAMATION MARK
{0x00A2, 0xA2},	// CENT SIGN
{0x00A3, 0xA3},	// POUND SIGN
{0x00A5, 0xB4},	// YEN SIGN
{0x00A7, 0xA4},	// SECTION SIGN
{0x00A8, 0xAC},	// DIAERESIS
{0x00A9, 0xA9},	// COPYRIGHT SIGN
{0x00AA, 0xBB},	// FEMININE ORDINAL INDICATOR
{0x00AB, 0xC7},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xC2},	// NOT SIGN
{0x00AE, 0xA8},	// REGISTERED SIGN
{0x00AF, 0xF8},	// MACRON
{0x00B0, 0xA1},	// DEGREE SIGN
{0x00B1, 0xB1},	// PLUS-MINUS SIGN
{0x00B4, 0xAB},	// ACUTE ACCENT
{0x00B5, 0xB5},	// MICRO SIGN
{0x00B6, 0xA6},	// PILCROW SIGN
{0x00B7, 0xE1},	// MIDDLE DOT
{0x00B8, 0xFC},	// CEDILLA
{0x00BA, 0xBC},	// MASCULINE ORDINAL INDICATOR
{0x00BB, 0xC8},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00BF, 0xC0},	// INVERTED QUESTION MARK
{0x00C0, 0xCB},	// LATIN CAPITAL LETTER A WITH GRAVE
{0x00C1, 0xE7},	// LATIN CAPITAL LETTER A WITH ACUTE
{0x00C2, 0xE5},	// LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00C3, 0xCC},	// LATIN CAPITAL LETTER A WITH TILDE
{0x00C4, 0x80},	// LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00C5, 0x81},	// LATIN CAPITAL LETTER A WITH RING ABOVE
{0x00C6, 0xAE},	// LATIN CAPITAL LETTER AE
{0x00C7, 0x82},	// LATIN CAPITAL LETTER C WITH CEDILLA
{0x00C8, 0xE9},	// LATIN CAPITAL LETTER E WITH GRAVE
{0x00C9, 0x83},	// LATIN CAPITAL LETTER E WITH ACUTE
{0x00CA, 0xE6},	// LATIN CAPITAL LETTER E WITH CIRCUMFLEX
{0x00CB, 0xE8},	// LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00CC, 0xED},	// LATIN CAPITAL LETTER I WITH GRAVE
{0x00CD, 0xEA},	// LATIN CAPITAL LETTER I WITH ACUTE
{0x00CE, 0xEB},	// LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00CF, 0xEC},	// LATIN CAPITAL LETTER I WITH DIAERESIS
{0x00D1, 0x84},	// LATIN CAPITAL LETTER N WITH TILDE
{0x00D2, 0xF1},	// LATIN CAPITAL LETTER O WITH GRAVE
{0x00D3, 0xEE},	// LATIN CAPITAL LETTER O WITH ACUTE
{0x00D4, 0xEF},	// LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00D5, 0xCD},	// LATIN CAPITAL LETTER O WITH TILDE
{0x00D6, 0x85},	// LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00D8, 0xAF},	// LATIN CAPITAL LETTER O WITH STROKE
{0x00D9, 0xF4},	// LATIN CAPITAL LETTER U WITH GRAVE
{0x00DA, 0xF2},	// LATIN CAPITAL LETTER U WITH ACUTE
{0x00DB, 0xF3},	// LATIN CAPITAL LETTER U WITH CIRCUMFLEX
{0x00DC, 0x86},	// LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00DF, 0xA7},	// LATIN SMALL LETTER SHARP S
{0x00E0, 0x88},	// LATIN SMALL LETTER A WITH GRAVE
{0x00E1, 0x87},	// LATIN SMALL LETTER A WITH ACUTE
{0x00E2, 0x89},	// LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00E3, 0x8B},	// LATIN SMALL LETTER A WITH TILDE
{0x00E4, 0x8A},	// LATIN SMALL LETTER A WITH DIAERESIS
{0x00E5, 0x8C},	// LATIN SMALL LETTER A WITH RING ABOVE
{0x00E6, 0xBE},	// LATIN SMALL LETTER AE
{0x00E7, 0x8D},	// LATIN SMALL LETTER C WITH CEDILLA
{0x00E8, 0x8F},	// LATIN SMALL LETTER E WITH GRAVE
{0x00E9, 0x8E},	// LATIN SMALL LETTER E WITH ACUTE
{0x00EA, 0x90},	// LATIN SMALL LETTER E WITH CIRCUMFLEX
{0x00EB, 0x91},	// LATIN SMALL LETTER E WITH DIAERESIS
{0x00EC, 0x93},	// LATIN SMALL LETTER I WITH GRAVE
{0x00ED, 0x92},	// LATIN SMALL LETTER I WITH ACUTE
{0x00EE, 0x94},	// LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00EF, 0x95},	// LATIN SMALL LETTER I WITH DIAERESIS
{0x00F1, 0x96},	// LATIN SMALL LETTER N WITH TILDE
{0x00F2, 0x98},	// LATIN SMALL LETTER O WITH GRAVE
{0x00F3, 0x97},	// LATIN SMALL LETTER O WITH ACUTE
{0x00F4, 0x99},	// LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00F5, 0x9B},	// LATIN SMALL LETTER O WITH TILDE
{0x00F6, 0x9A},	// LATIN SMALL LETTER O WITH DIAERESIS
{0x00F7, 0xD6},	// DIVISION SIGN
{0x00F8, 0xBF},	// LATIN SMALL LETTER O WITH STROKE
{0x00F9, 0x9D},	// LATIN SMALL LETTER U WITH GRAVE
{0x00FA, 0x9C},	// LATIN SMALL LETTER U WITH ACUTE
{0x00FB, 0x9E},	// LATIN SMALL LETTER U WITH CIRCUMFLEX
{0x00FC, 0x9F},	// LATIN SMALL LETTER U WITH DIAERESIS
{0x00FF, 0xD8},	// LATIN SMALL LETTER Y WITH DIAERESIS
{0x0131, 0xF5},	// LATIN SMALL LETTER DOTLESS I
{0x0152, 0xCE},	// LATIN CAPITAL LIGATURE OE
{0x0153, 0xCF},	// LATIN SMALL LIGATURE OE
{0x0178, 0xD9},	// LATIN CAPITAL LETTER Y WITH DIAERESIS
{0x0192, 0xC4},	// LATIN SMALL LETTER F WITH HOOK
{0x02C6, 0xF6},	// MODIFIER LETTER CIRCUMFLEX ACCENT
{0x02C7, 0xFF},	// CARON
{0x02D8, 0xF9},	// BREVE
{0x02D9, 0xFA},	// DOT ABOVE
{0x02DA, 0xFB},	// RING ABOVE
{0x02DB, 0xFE},	// OGONEK
{0x02DC, 0xF7},	// SMALL TILDE
{0x02DD, 0xFD},	// DOUBLE ACUTE ACCENT
{0x03A9, 0xBD},	// GREEK CAPITAL LETTER OMEGA
{0x03C0, 0xB9},	// GREEK SMALL LETTER PI
{0x2013, 0xD0},	// EN DASH
{0x2014, 0xD1},	// EM DASH
{0x2018, 0xD4},	// LEFT SINGLE QUOTATION MARK
{0x2019, 0xD5},	// RIGHT SINGLE QUOTATION MARK
{0x201A, 0xE2},	// SINGLE LOW-9 QUOTATION MARK
{0x201C, 0xD2},	// LEFT DOUBLE QUOTATION MARK
{0x201D, 0xD3},	// RIGHT DOUBLE QUOTATION MARK
{0x201E, 0xE3},	// DOUBLE LOW-9 QUOTATION MARK
{0x2020, 0xA0},	// DAGGER
{0x2021, 0xE0},	// DOUBLE DAGGER
{0x2022, 0xA5},	// BULLET
{0x2026, 0xC9},	// HORIZONTAL ELLIPSIS
{0x2030, 0xE4},	// PER MILLE SIGN
{0x2039, 0xDC},	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
{0x203A, 0xDD},	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
{0x2044, 0xDA},	// FRACTION SLASH
{0x20AC, 0xDB},	// EURO SIGN
{0x2122, 0xAA},	// TRADE MARK SIGN
{0x2202, 0xB6},	// PARTIAL DIFFERENTIAL
{0x2206, 0xC6},	// INCREMENT
{0x220F, 0xB8},	// N-ARY PRODUCT
{0x2211, 0xB7},	// N-ARY SUMMATION
{0x221A, 0xC3},	// SQUARE ROOT
{0x221E, 0xB0},	// INFINITY
{0x222B, 0xBA},	// INTEGRAL
{0x2248, 0xC5},	// ALMOST EQUAL TO
{0x2260, 0xAD},	// NOT EQUAL TO
{0x2264, 0xB2},	// LESS-THAN OR EQUAL TO
{0x2265, 0xB3},	// GREATER-THAN OR EQUAL TO
{0x25CA, 0xD7},	// LOZENGE
{0xF8FF, 0xF0},	// Apple logo (copyrighted)
{0xFB01, 0xDE},	// LATIN SMALL LIGATURE FI
{0xFB02, 0xDF}	// LATIN SMALL LIGATURE FL
};

static const unsigned short from8859_15[] = {
    0x00A0,	// 0xA0	NO-BREAK SPACE
    0x00A1,	// 0xA1	INVERTED EXCLAMATION MARK
    0x00A2,	// 0xA2	CENT SIGN
    0x00A3,	// 0xA3	POUND SIGN
    0x20AC,	// 0xA4	EURO SIGN
    0x00A5,	// 0xA5	YEN SIGN
    0x0160,	// 0xA6	LATIN CAPITAL LETTER S WITH CARON
    0x00A7,	// 0xA7	SECTION SIGN
    0x0161,	// 0xA8	LATIN SMALL LETTER S WITH CARON
    0x00A9,	// 0xA9	COPYRIGHT SIGN
    0x00AA,	// 0xAA	FEMININE ORDINAL INDICATOR
    0x00AB,	// 0xAB	LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00AC,	// 0xAC	NOT SIGN
    0x00AD,	// 0xAD	SOFT HYPHEN
    0x00AE,	// 0xAE	REGISTERED SIGN
    0x00AF,	// 0xAF	MACRON
    0x00B0,	// 0xB0	DEGREE SIGN
    0x00B1,	// 0xB1	PLUS-MINUS SIGN
    0x00B2,	// 0xB2	SUPERSCRIPT TWO
    0x00B3,	// 0xB3	SUPERSCRIPT THREE
    0x017D,	// 0xB4	LATIN CAPITAL LETTER Z WITH CARON
    0x00B5,	// 0xB5	MICRO SIGN
    0x00B6,	// 0xB6	PILCROW SIGN
    0x00B7,	// 0xB7	MIDDLE DOT
    0x017E,	// 0xB8	LATIN SMALL LETTER Z WITH CARON
    0x00B9,	// 0xB9	SUPERSCRIPT ONE
    0x00BA,	// 0xBA	MASCULINE ORDINAL INDICATOR
    0x00BB,	// 0xBB	RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x0152,	// 0xBC	LATIN CAPITAL LIGATURE OE
    0x0153,	// 0xBD	LATIN SMALL LIGATURE OE
    0x0178,	// 0xBE	LATIN CAPITAL LETTER Y WITH DIAERESIS
    0x00BF,	// 0xBF	INVERTED QUESTION MARK
    0x00C0,	// 0xC0	LATIN CAPITAL LETTER A WITH GRAVE
    0x00C1,	// 0xC1	LATIN CAPITAL LETTER A WITH ACUTE
    0x00C2,	// 0xC2	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
    0x00C3,	// 0xC3	LATIN CAPITAL LETTER A WITH TILDE
    0x00C4,	// 0xC4	LATIN CAPITAL LETTER A WITH DIAERESIS
    0x00C5,	// 0xC5	LATIN CAPITAL LETTER A WITH RING ABOVE
    0x00C6,	// 0xC6	LATIN CAPITAL LETTER AE
    0x00C7,	// 0xC7	LATIN CAPITAL LETTER C WITH CEDILLA
    0x00C8,	// 0xC8	LATIN CAPITAL LETTER E WITH GRAVE
    0x00C9,	// 0xC9	LATIN CAPITAL LETTER E WITH ACUTE
    0x00CA,	// 0xCA	LATIN CAPITAL LETTER E WITH CIRCUMFLEX
    0x00CB,	// 0xCB	LATIN CAPITAL LETTER E WITH DIAERESIS
    0x00CC,	// 0xCC	LATIN CAPITAL LETTER I WITH GRAVE
    0x00CD,	// 0xCD	LATIN CAPITAL LETTER I WITH ACUTE
    0x00CE,	// 0xCE	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
    0x00CF,	// 0xCF	LATIN CAPITAL LETTER I WITH DIAERESIS
    0x00D0,	// 0xD0	LATIN CAPITAL LETTER ETH
    0x00D1,	// 0xD1	LATIN CAPITAL LETTER N WITH TILDE
    0x00D2,	// 0xD2	LATIN CAPITAL LETTER O WITH GRAVE
    0x00D3,	// 0xD3	LATIN CAPITAL LETTER O WITH ACUTE
    0x00D4,	// 0xD4	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
    0x00D5,	// 0xD5	LATIN CAPITAL LETTER O WITH TILDE
    0x00D6,	// 0xD6	LATIN CAPITAL LETTER O WITH DIAERESIS
    0x00D7,	// 0xD7	MULTIPLICATION SIGN
    0x00D8,	// 0xD8	LATIN CAPITAL LETTER O WITH STROKE
    0x00D9,	// 0xD9	LATIN CAPITAL LETTER U WITH GRAVE
    0x00DA,	// 0xDA	LATIN CAPITAL LETTER U WITH ACUTE
    0x00DB,	// 0xDB	LATIN CAPITAL LETTER U WITH CIRCUMFLEX
    0x00DC,	// 0xDC	LATIN CAPITAL LETTER U WITH DIAERESIS
    0x00DD,	// 0xDD	LATIN CAPITAL LETTER Y WITH ACUTE
    0x00DE,	// 0xDE	LATIN CAPITAL LETTER THORN
    0x00DF,	// 0xDF	LATIN SMALL LETTER SHARP S
    0x00E0,	// 0xE0	LATIN SMALL LETTER A WITH GRAVE
    0x00E1,	// 0xE1	LATIN SMALL LETTER A WITH ACUTE
    0x00E2,	// 0xE2	LATIN SMALL LETTER A WITH CIRCUMFLEX
    0x00E3,	// 0xE3	LATIN SMALL LETTER A WITH TILDE
    0x00E4,	// 0xE4	LATIN SMALL LETTER A WITH DIAERESIS
    0x00E5,	// 0xE5	LATIN SMALL LETTER A WITH RING ABOVE
    0x00E6,	// 0xE6	LATIN SMALL LETTER AE
    0x00E7,	// 0xE7	LATIN SMALL LETTER C WITH CEDILLA
    0x00E8,	// 0xE8	LATIN SMALL LETTER E WITH GRAVE
    0x00E9,	// 0xE9	LATIN SMALL LETTER E WITH ACUTE
    0x00EA,	// 0xEA	LATIN SMALL LETTER E WITH CIRCUMFLEX
    0x00EB,	// 0xEB	LATIN SMALL LETTER E WITH DIAERESIS
    0x00EC,	// 0xEC	LATIN SMALL LETTER I WITH GRAVE
    0x00ED,	// 0xED	LATIN SMALL LETTER I WITH ACUTE
    0x00EE,	// 0xEE	LATIN SMALL LETTER I WITH CIRCUMFLEX
    0x00EF,	// 0xEF	LATIN SMALL LETTER I WITH DIAERESIS
    0x00F0,	// 0xF0	LATIN SMALL LETTER ETH
    0x00F1,	// 0xF1	LATIN SMALL LETTER N WITH TILDE
    0x00F2,	// 0xF2	LATIN SMALL LETTER O WITH GRAVE
    0x00F3,	// 0xF3	LATIN SMALL LETTER O WITH ACUTE
    0x00F4,	// 0xF4	LATIN SMALL LETTER O WITH CIRCUMFLEX
    0x00F5,	// 0xF5	LATIN SMALL LETTER O WITH TILDE
    0x00F6,	// 0xF6	LATIN SMALL LETTER O WITH DIAERESIS
    0x00F7,	// 0xF7	DIVISION SIGN
    0x00F8,	// 0xF8	LATIN SMALL LETTER O WITH STROKE
    0x00F9,	// 0xF9	LATIN SMALL LETTER U WITH GRAVE
    0x00FA,	// 0xFA	LATIN SMALL LETTER U WITH ACUTE
    0x00FB,	// 0xFB	LATIN SMALL LETTER U WITH CIRCUMFLEX
    0x00FC,	// 0xFC	LATIN SMALL LETTER U WITH DIAERESIS
    0x00FD,	// 0xFD	LATIN SMALL LETTER Y WITH ACUTE
    0x00FE,	// 0xFE	LATIN SMALL LETTER THORN
    0x00FF	// 0xFF	LATIN SMALL LETTER Y WITH DIAERESIS
};

static const CharSetCvt::MapEnt to8859_15[] = {
{0x00A0, 0xA0},	//	NO-BREAK SPACE
{0x00A1, 0xA1},	//	INVERTED EXCLAMATION MARK
{0x00A2, 0xA2},	//	CENT SIGN
{0x00A3, 0xA3},	//	POUND SIGN
{0x00A5, 0xA5},	//	YEN SIGN
{0x00A7, 0xA7},	//	SECTION SIGN
{0x00A9, 0xA9},	//	COPYRIGHT SIGN
{0x00AA, 0xAA},	//	FEMININE ORDINAL INDICATOR
{0x00AB, 0xAB},	//	LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xAC},	//	NOT SIGN
{0x00AD, 0xAD},	//	SOFT HYPHEN
{0x00AE, 0xAE},	//	REGISTERED SIGN
{0x00AF, 0xAF},	//	MACRON
{0x00B0, 0xB0},	//	DEGREE SIGN
{0x00B1, 0xB1},	//	PLUS-MINUS SIGN
{0x00B2, 0xB2},	//	SUPERSCRIPT TWO
{0x00B3, 0xB3},	//	SUPERSCRIPT THREE
{0x00B5, 0xB5},	//	MICRO SIGN
{0x00B6, 0xB6},	//	PILCROW SIGN
{0x00B7, 0xB7},	//	MIDDLE DOT
{0x00B9, 0xB9},	//	SUPERSCRIPT ONE
{0x00BA, 0xBA},	//	MASCULINE ORDINAL INDICATOR
{0x00BB, 0xBB},	//	RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00BF, 0xBF},	//	INVERTED QUESTION MARK
{0x00C0, 0xC0},	//	LATIN CAPITAL LETTER A WITH GRAVE
{0x00C1, 0xC1},	//	LATIN CAPITAL LETTER A WITH ACUTE
{0x00C2, 0xC2},	//	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00C3, 0xC3},	//	LATIN CAPITAL LETTER A WITH TILDE
{0x00C4, 0xC4},	//	LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00C5, 0xC5},	//	LATIN CAPITAL LETTER A WITH RING ABOVE
{0x00C6, 0xC6},	//	LATIN CAPITAL LETTER AE
{0x00C7, 0xC7},	//	LATIN CAPITAL LETTER C WITH CEDILLA
{0x00C8, 0xC8},	//	LATIN CAPITAL LETTER E WITH GRAVE
{0x00C9, 0xC9},	//	LATIN CAPITAL LETTER E WITH ACUTE
{0x00CA, 0xCA},	//	LATIN CAPITAL LETTER E WITH CIRCUMFLEX
{0x00CB, 0xCB},	//	LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00CC, 0xCC},	//	LATIN CAPITAL LETTER I WITH GRAVE
{0x00CD, 0xCD},	//	LATIN CAPITAL LETTER I WITH ACUTE
{0x00CE, 0xCE},	//	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00CF, 0xCF},	//	LATIN CAPITAL LETTER I WITH DIAERESIS
{0x00D0, 0xD0},	//	LATIN CAPITAL LETTER ETH
{0x00D1, 0xD1},	//	LATIN CAPITAL LETTER N WITH TILDE
{0x00D2, 0xD2},	//	LATIN CAPITAL LETTER O WITH GRAVE
{0x00D3, 0xD3},	//	LATIN CAPITAL LETTER O WITH ACUTE
{0x00D4, 0xD4},	//	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00D5, 0xD5},	//	LATIN CAPITAL LETTER O WITH TILDE
{0x00D6, 0xD6},	//	LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00D7, 0xD7},	//	MULTIPLICATION SIGN
{0x00D8, 0xD8},	//	LATIN CAPITAL LETTER O WITH STROKE
{0x00D9, 0xD9},	//	LATIN CAPITAL LETTER U WITH GRAVE
{0x00DA, 0xDA},	//	LATIN CAPITAL LETTER U WITH ACUTE
{0x00DB, 0xDB},	//	LATIN CAPITAL LETTER U WITH CIRCUMFLEX
{0x00DC, 0xDC},	//	LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00DD, 0xDD},	//	LATIN CAPITAL LETTER Y WITH ACUTE
{0x00DE, 0xDE},	//	LATIN CAPITAL LETTER THORN
{0x00DF, 0xDF},	//	LATIN SMALL LETTER SHARP S
{0x00E0, 0xE0},	//	LATIN SMALL LETTER A WITH GRAVE
{0x00E1, 0xE1},	//	LATIN SMALL LETTER A WITH ACUTE
{0x00E2, 0xE2},	//	LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00E3, 0xE3},	//	LATIN SMALL LETTER A WITH TILDE
{0x00E4, 0xE4},	//	LATIN SMALL LETTER A WITH DIAERESIS
{0x00E5, 0xE5},	//	LATIN SMALL LETTER A WITH RING ABOVE
{0x00E6, 0xE6},	//	LATIN SMALL LETTER AE
{0x00E7, 0xE7},	//	LATIN SMALL LETTER C WITH CEDILLA
{0x00E8, 0xE8},	//	LATIN SMALL LETTER E WITH GRAVE
{0x00E9, 0xE9},	//	LATIN SMALL LETTER E WITH ACUTE
{0x00EA, 0xEA},	//	LATIN SMALL LETTER E WITH CIRCUMFLEX
{0x00EB, 0xEB},	//	LATIN SMALL LETTER E WITH DIAERESIS
{0x00EC, 0xEC},	//	LATIN SMALL LETTER I WITH GRAVE
{0x00ED, 0xED},	//	LATIN SMALL LETTER I WITH ACUTE
{0x00EE, 0xEE},	//	LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00EF, 0xEF},	//	LATIN SMALL LETTER I WITH DIAERESIS
{0x00F0, 0xF0},	//	LATIN SMALL LETTER ETH
{0x00F1, 0xF1},	//	LATIN SMALL LETTER N WITH TILDE
{0x00F2, 0xF2},	//	LATIN SMALL LETTER O WITH GRAVE
{0x00F3, 0xF3},	//	LATIN SMALL LETTER O WITH ACUTE
{0x00F4, 0xF4},	//	LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00F5, 0xF5},	//	LATIN SMALL LETTER O WITH TILDE
{0x00F6, 0xF6},	//	LATIN SMALL LETTER O WITH DIAERESIS
{0x00F7, 0xF7},	//	DIVISION SIGN
{0x00F8, 0xF8},	//	LATIN SMALL LETTER O WITH STROKE
{0x00F9, 0xF9},	//	LATIN SMALL LETTER U WITH GRAVE
{0x00FA, 0xFA},	//	LATIN SMALL LETTER U WITH ACUTE
{0x00FB, 0xFB},	//	LATIN SMALL LETTER U WITH CIRCUMFLEX
{0x00FC, 0xFC},	//	LATIN SMALL LETTER U WITH DIAERESIS
{0x00FD, 0xFD},	//	LATIN SMALL LETTER Y WITH ACUTE
{0x00FE, 0xFE},	//	LATIN SMALL LETTER THORN
{0x00FF, 0xFF},	//	LATIN SMALL LETTER Y WITH DIAERESIS
{0x0152, 0xBC},	//	LATIN CAPITAL LIGATURE OE
{0x0153, 0xBD},	//	LATIN SMALL LIGATURE OE
{0x0160, 0xA6},	//	LATIN CAPITAL LETTER S WITH CARON
{0x0161, 0xA8},	//	LATIN SMALL LETTER S WITH CARON
{0x0178, 0xBE},	//	LATIN CAPITAL LETTER Y WITH DIAERESIS
{0x017D, 0xB4},	//	LATIN CAPITAL LETTER Z WITH CARON
{0x017E, 0xB8},	//	LATIN SMALL LETTER Z WITH CARON
{0x20AC, 0xA4}	//	EURO SIGN
};

static const unsigned short from8859_5[] = {
    0x00A0,	//	NO-BREAK SPACE
    0x0401,	//	CYRILLIC CAPITAL LETTER IO
    0x0402,	//	CYRILLIC CAPITAL LETTER DJE
    0x0403,	//	CYRILLIC CAPITAL LETTER GJE
    0x0404,	//	CYRILLIC CAPITAL LETTER UKRAINIAN IE
    0x0405,	//	CYRILLIC CAPITAL LETTER DZE
    0x0406,	//	CYRILLIC CAPITAL LETTER BYELORUSSIAN-UKRAINIAN I
    0x0407,	//	CYRILLIC CAPITAL LETTER YI
    0x0408,	//	CYRILLIC CAPITAL LETTER JE
    0x0409,	//	CYRILLIC CAPITAL LETTER LJE
    0x040A,	//	CYRILLIC CAPITAL LETTER NJE
    0x040B,	//	CYRILLIC CAPITAL LETTER TSHE
    0x040C,	//	CYRILLIC CAPITAL LETTER KJE
    0x00AD,	//	SOFT HYPHEN
    0x040E,	//	CYRILLIC CAPITAL LETTER SHORT U
    0x040F,	//	CYRILLIC CAPITAL LETTER DZHE
    0x0410,	//	CYRILLIC CAPITAL LETTER A
    0x0411,	//	CYRILLIC CAPITAL LETTER BE
    0x0412,	//	CYRILLIC CAPITAL LETTER VE
    0x0413,	//	CYRILLIC CAPITAL LETTER GHE
    0x0414,	//	CYRILLIC CAPITAL LETTER DE
    0x0415,	//	CYRILLIC CAPITAL LETTER IE
    0x0416,	//	CYRILLIC CAPITAL LETTER ZHE
    0x0417,	//	CYRILLIC CAPITAL LETTER ZE
    0x0418,	//	CYRILLIC CAPITAL LETTER I
    0x0419,	//	CYRILLIC CAPITAL LETTER SHORT I
    0x041A,	//	CYRILLIC CAPITAL LETTER KA
    0x041B,	//	CYRILLIC CAPITAL LETTER EL
    0x041C,	//	CYRILLIC CAPITAL LETTER EM
    0x041D,	//	CYRILLIC CAPITAL LETTER EN
    0x041E,	//	CYRILLIC CAPITAL LETTER O
    0x041F,	//	CYRILLIC CAPITAL LETTER PE
    0x0420,	//	CYRILLIC CAPITAL LETTER ER
    0x0421,	//	CYRILLIC CAPITAL LETTER ES
    0x0422,	//	CYRILLIC CAPITAL LETTER TE
    0x0423,	//	CYRILLIC CAPITAL LETTER U
    0x0424,	//	CYRILLIC CAPITAL LETTER EF
    0x0425,	//	CYRILLIC CAPITAL LETTER HA
    0x0426,	//	CYRILLIC CAPITAL LETTER TSE
    0x0427,	//	CYRILLIC CAPITAL LETTER CHE
    0x0428,	//	CYRILLIC CAPITAL LETTER SHA
    0x0429,	//	CYRILLIC CAPITAL LETTER SHCHA
    0x042A,	//	CYRILLIC CAPITAL LETTER HARD SIGN
    0x042B,	//	CYRILLIC CAPITAL LETTER YERU
    0x042C,	//	CYRILLIC CAPITAL LETTER SOFT SIGN
    0x042D,	//	CYRILLIC CAPITAL LETTER E
    0x042E,	//	CYRILLIC CAPITAL LETTER YU
    0x042F,	//	CYRILLIC CAPITAL LETTER YA
    0x0430,	//	CYRILLIC SMALL LETTER A
    0x0431,	//	CYRILLIC SMALL LETTER BE
    0x0432,	//	CYRILLIC SMALL LETTER VE
    0x0433,	//	CYRILLIC SMALL LETTER GHE
    0x0434,	//	CYRILLIC SMALL LETTER DE
    0x0435,	//	CYRILLIC SMALL LETTER IE
    0x0436,	//	CYRILLIC SMALL LETTER ZHE
    0x0437,	//	CYRILLIC SMALL LETTER ZE
    0x0438,	//	CYRILLIC SMALL LETTER I
    0x0439,	//	CYRILLIC SMALL LETTER SHORT I
    0x043A,	//	CYRILLIC SMALL LETTER KA
    0x043B,	//	CYRILLIC SMALL LETTER EL
    0x043C,	//	CYRILLIC SMALL LETTER EM
    0x043D,	//	CYRILLIC SMALL LETTER EN
    0x043E,	//	CYRILLIC SMALL LETTER O
    0x043F,	//	CYRILLIC SMALL LETTER PE
    0x0440,	//	CYRILLIC SMALL LETTER ER
    0x0441,	//	CYRILLIC SMALL LETTER ES
    0x0442,	//	CYRILLIC SMALL LETTER TE
    0x0443,	//	CYRILLIC SMALL LETTER U
    0x0444,	//	CYRILLIC SMALL LETTER EF
    0x0445,	//	CYRILLIC SMALL LETTER HA
    0x0446,	//	CYRILLIC SMALL LETTER TSE
    0x0447,	//	CYRILLIC SMALL LETTER CHE
    0x0448,	//	CYRILLIC SMALL LETTER SHA
    0x0449,	//	CYRILLIC SMALL LETTER SHCHA
    0x044A,	//	CYRILLIC SMALL LETTER HARD SIGN
    0x044B,	//	CYRILLIC SMALL LETTER YERU
    0x044C,	//	CYRILLIC SMALL LETTER SOFT SIGN
    0x044D,	//	CYRILLIC SMALL LETTER E
    0x044E,	//	CYRILLIC SMALL LETTER YU
    0x044F,	//	CYRILLIC SMALL LETTER YA
    0x2116,	//	NUMERO SIGN
    0x0451,	//	CYRILLIC SMALL LETTER IO
    0x0452,	//	CYRILLIC SMALL LETTER DJE
    0x0453,	//	CYRILLIC SMALL LETTER GJE
    0x0454,	//	CYRILLIC SMALL LETTER UKRAINIAN IE
    0x0455,	//	CYRILLIC SMALL LETTER DZE
    0x0456,	//	CYRILLIC SMALL LETTER BYELORUSSIAN-UKRAINIAN I
    0x0457,	//	CYRILLIC SMALL LETTER YI
    0x0458,	//	CYRILLIC SMALL LETTER JE
    0x0459,	//	CYRILLIC SMALL LETTER LJE
    0x045A,	//	CYRILLIC SMALL LETTER NJE
    0x045B,	//	CYRILLIC SMALL LETTER TSHE
    0x045C,	//	CYRILLIC SMALL LETTER KJE
    0x00A7,	//	SECTION SIGN
    0x045E,	//	CYRILLIC SMALL LETTER SHORT U
    0x045F	//	CYRILLIC SMALL LETTER DZHE
};

static const CharSetCvt::MapEnt to8859_5[] = {
{0x00A0, 0xA0},	//	NO-BREAK SPACE
{0x00A7, 0xFD},	//	SECTION SIGN
{0x00AD, 0xAD},	//	SOFT HYPHEN
{0x0401, 0xA1},	//	CYRILLIC CAPITAL LETTER IO
{0x0402, 0xA2},	//	CYRILLIC CAPITAL LETTER DJE
{0x0403, 0xA3},	//	CYRILLIC CAPITAL LETTER GJE
{0x0404, 0xA4},	//	CYRILLIC CAPITAL LETTER UKRAINIAN IE
{0x0405, 0xA5},	//	CYRILLIC CAPITAL LETTER DZE
{0x0406, 0xA6},	//	CYRILLIC CAPITAL LETTER BYELORUSSIAN-UKRAINIAN I
{0x0407, 0xA7},	//	CYRILLIC CAPITAL LETTER YI
{0x0408, 0xA8},	//	CYRILLIC CAPITAL LETTER JE
{0x0409, 0xA9},	//	CYRILLIC CAPITAL LETTER LJE
{0x040A, 0xAA},	//	CYRILLIC CAPITAL LETTER NJE
{0x040B, 0xAB},	//	CYRILLIC CAPITAL LETTER TSHE
{0x040C, 0xAC},	//	CYRILLIC CAPITAL LETTER KJE
{0x040E, 0xAE},	//	CYRILLIC CAPITAL LETTER SHORT U
{0x040F, 0xAF},	//	CYRILLIC CAPITAL LETTER DZHE
{0x0410, 0xB0},	//	CYRILLIC CAPITAL LETTER A
{0x0411, 0xB1},	//	CYRILLIC CAPITAL LETTER BE
{0x0412, 0xB2},	//	CYRILLIC CAPITAL LETTER VE
{0x0413, 0xB3},	//	CYRILLIC CAPITAL LETTER GHE
{0x0414, 0xB4},	//	CYRILLIC CAPITAL LETTER DE
{0x0415, 0xB5},	//	CYRILLIC CAPITAL LETTER IE
{0x0416, 0xB6},	//	CYRILLIC CAPITAL LETTER ZHE
{0x0417, 0xB7},	//	CYRILLIC CAPITAL LETTER ZE
{0x0418, 0xB8},	//	CYRILLIC CAPITAL LETTER I
{0x0419, 0xB9},	//	CYRILLIC CAPITAL LETTER SHORT I
{0x041A, 0xBA},	//	CYRILLIC CAPITAL LETTER KA
{0x041B, 0xBB},	//	CYRILLIC CAPITAL LETTER EL
{0x041C, 0xBC},	//	CYRILLIC CAPITAL LETTER EM
{0x041D, 0xBD},	//	CYRILLIC CAPITAL LETTER EN
{0x041E, 0xBE},	//	CYRILLIC CAPITAL LETTER O
{0x041F, 0xBF},	//	CYRILLIC CAPITAL LETTER PE
{0x0420, 0xC0},	//	CYRILLIC CAPITAL LETTER ER
{0x0421, 0xC1},	//	CYRILLIC CAPITAL LETTER ES
{0x0422, 0xC2},	//	CYRILLIC CAPITAL LETTER TE
{0x0423, 0xC3},	//	CYRILLIC CAPITAL LETTER U
{0x0424, 0xC4},	//	CYRILLIC CAPITAL LETTER EF
{0x0425, 0xC5},	//	CYRILLIC CAPITAL LETTER HA
{0x0426, 0xC6},	//	CYRILLIC CAPITAL LETTER TSE
{0x0427, 0xC7},	//	CYRILLIC CAPITAL LETTER CHE
{0x0428, 0xC8},	//	CYRILLIC CAPITAL LETTER SHA
{0x0429, 0xC9},	//	CYRILLIC CAPITAL LETTER SHCHA
{0x042A, 0xCA},	//	CYRILLIC CAPITAL LETTER HARD SIGN
{0x042B, 0xCB},	//	CYRILLIC CAPITAL LETTER YERU
{0x042C, 0xCC},	//	CYRILLIC CAPITAL LETTER SOFT SIGN
{0x042D, 0xCD},	//	CYRILLIC CAPITAL LETTER E
{0x042E, 0xCE},	//	CYRILLIC CAPITAL LETTER YU
{0x042F, 0xCF},	//	CYRILLIC CAPITAL LETTER YA
{0x0430, 0xD0},	//	CYRILLIC SMALL LETTER A
{0x0431, 0xD1},	//	CYRILLIC SMALL LETTER BE
{0x0432, 0xD2},	//	CYRILLIC SMALL LETTER VE
{0x0433, 0xD3},	//	CYRILLIC SMALL LETTER GHE
{0x0434, 0xD4},	//	CYRILLIC SMALL LETTER DE
{0x0435, 0xD5},	//	CYRILLIC SMALL LETTER IE
{0x0436, 0xD6},	//	CYRILLIC SMALL LETTER ZHE
{0x0437, 0xD7},	//	CYRILLIC SMALL LETTER ZE
{0x0438, 0xD8},	//	CYRILLIC SMALL LETTER I
{0x0439, 0xD9},	//	CYRILLIC SMALL LETTER SHORT I
{0x043A, 0xDA},	//	CYRILLIC SMALL LETTER KA
{0x043B, 0xDB},	//	CYRILLIC SMALL LETTER EL
{0x043C, 0xDC},	//	CYRILLIC SMALL LETTER EM
{0x043D, 0xDD},	//	CYRILLIC SMALL LETTER EN
{0x043E, 0xDE},	//	CYRILLIC SMALL LETTER O
{0x043F, 0xDF},	//	CYRILLIC SMALL LETTER PE
{0x0440, 0xE0},	//	CYRILLIC SMALL LETTER ER
{0x0441, 0xE1},	//	CYRILLIC SMALL LETTER ES
{0x0442, 0xE2},	//	CYRILLIC SMALL LETTER TE
{0x0443, 0xE3},	//	CYRILLIC SMALL LETTER U
{0x0444, 0xE4},	//	CYRILLIC SMALL LETTER EF
{0x0445, 0xE5},	//	CYRILLIC SMALL LETTER HA
{0x0446, 0xE6},	//	CYRILLIC SMALL LETTER TSE
{0x0447, 0xE7},	//	CYRILLIC SMALL LETTER CHE
{0x0448, 0xE8},	//	CYRILLIC SMALL LETTER SHA
{0x0449, 0xE9},	//	CYRILLIC SMALL LETTER SHCHA
{0x044A, 0xEA},	//	CYRILLIC SMALL LETTER HARD SIGN
{0x044B, 0xEB},	//	CYRILLIC SMALL LETTER YERU
{0x044C, 0xEC},	//	CYRILLIC SMALL LETTER SOFT SIGN
{0x044D, 0xED},	//	CYRILLIC SMALL LETTER E
{0x044E, 0xEE},	//	CYRILLIC SMALL LETTER YU
{0x044F, 0xEF},	//	CYRILLIC SMALL LETTER YA
{0x0451, 0xF1},	//	CYRILLIC SMALL LETTER IO
{0x0452, 0xF2},	//	CYRILLIC SMALL LETTER DJE
{0x0453, 0xF3},	//	CYRILLIC SMALL LETTER GJE
{0x0454, 0xF4},	//	CYRILLIC SMALL LETTER UKRAINIAN IE
{0x0455, 0xF5},	//	CYRILLIC SMALL LETTER DZE
{0x0456, 0xF6},	//	CYRILLIC SMALL LETTER BYELORUSSIAN-UKRAINIAN I
{0x0457, 0xF7},	//	CYRILLIC SMALL LETTER YI
{0x0458, 0xF8},	//	CYRILLIC SMALL LETTER JE
{0x0459, 0xF9},	//	CYRILLIC SMALL LETTER LJE
{0x045A, 0xFA},	//	CYRILLIC SMALL LETTER NJE
{0x045B, 0xFB},	//	CYRILLIC SMALL LETTER TSHE
{0x045C, 0xFC},	//	CYRILLIC SMALL LETTER KJE
{0x045E, 0xFE},	//	CYRILLIC SMALL LETTER SHORT U
{0x045F, 0xFF},	//	CYRILLIC SMALL LETTER DZHE
{0x2116, 0xF0}	//	NUMERO SIGN
};

static const unsigned short fromKOI8_R[] = {
    0x2500,	//	BOX DRAWINGS LIGHT HORIZONTAL
    0x2502,	//	BOX DRAWINGS LIGHT VERTICAL
    0x250C,	//	BOX DRAWINGS LIGHT DOWN AND RIGHT
    0x2510,	//	BOX DRAWINGS LIGHT DOWN AND LEFT
    0x2514,	//	BOX DRAWINGS LIGHT UP AND RIGHT
    0x2518,	//	BOX DRAWINGS LIGHT UP AND LEFT
    0x251C,	//	BOX DRAWINGS LIGHT VERTICAL AND RIGHT
    0x2524,	//	BOX DRAWINGS LIGHT VERTICAL AND LEFT
    0x252C,	//	BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
    0x2534,	//	BOX DRAWINGS LIGHT UP AND HORIZONTAL
    0x253C,	//	BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
    0x2580,	//	UPPER HALF BLOCK
    0x2584,	//	LOWER HALF BLOCK
    0x2588,	//	FULL BLOCK
    0x258C,	//	LEFT HALF BLOCK
    0x2590,	//	RIGHT HALF BLOCK
    0x2591,	//	LIGHT SHADE
    0x2592,	//	MEDIUM SHADE
    0x2593,	//	DARK SHADE
    0x2320,	//	TOP HALF INTEGRAL
    0x25A0,	//	BLACK SQUARE
    0x2219,	//	BULLET OPERATOR
    0x221A,	//	SQUARE ROOT
    0x2248,	//	ALMOST EQUAL TO
    0x2264,	//	LESS-THAN OR EQUAL TO
    0x2265,	//	GREATER-THAN OR EQUAL TO
    0x00A0,	//	NO-BREAK SPACE
    0x2321,	//	BOTTOM HALF INTEGRAL
    0x00B0,	//	DEGREE SIGN
    0x00B2,	//	SUPERSCRIPT TWO
    0x00B7,	//	MIDDLE DOT
    0x00F7,	//	DIVISION SIGN
    0x2550,	//	BOX DRAWINGS DOUBLE HORIZONTAL
    0x2551,	//	BOX DRAWINGS DOUBLE VERTICAL
    0x2552,	//	BOX DRAWINGS DOWN SINGLE AND RIGHT DOUBLE
    0x0451,	//	CYRILLIC SMALL LETTER IO
    0x2553,	//	BOX DRAWINGS DOWN DOUBLE AND RIGHT SINGLE
    0x2554,	//	BOX DRAWINGS DOUBLE DOWN AND RIGHT
    0x2555,	//	BOX DRAWINGS DOWN SINGLE AND LEFT DOUBLE
    0x2556,	//	BOX DRAWINGS DOWN DOUBLE AND LEFT SINGLE
    0x2557,	//	BOX DRAWINGS DOUBLE DOWN AND LEFT
    0x2558,	//	BOX DRAWINGS UP SINGLE AND RIGHT DOUBLE
    0x2559,	//	BOX DRAWINGS UP DOUBLE AND RIGHT SINGLE
    0x255A,	//	BOX DRAWINGS DOUBLE UP AND RIGHT
    0x255B,	//	BOX DRAWINGS UP SINGLE AND LEFT DOUBLE
    0x255C,	//	BOX DRAWINGS UP DOUBLE AND LEFT SINGLE
    0x255D,	//	BOX DRAWINGS DOUBLE UP AND LEFT
    0x255E,	//	BOX DRAWINGS VERTICAL SINGLE AND RIGHT DOUBLE
    0x255F,	//	BOX DRAWINGS VERTICAL DOUBLE AND RIGHT SINGLE
    0x2560,	//	BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
    0x2561,	//	BOX DRAWINGS VERTICAL SINGLE AND LEFT DOUBLE
    0x0401,	//	CYRILLIC CAPITAL LETTER IO
    0x2562,	//	BOX DRAWINGS VERTICAL DOUBLE AND LEFT SINGLE
    0x2563,	//	BOX DRAWINGS DOUBLE VERTICAL AND LEFT
    0x2564,	//	BOX DRAWINGS DOWN SINGLE AND HORIZONTAL DOUBLE
    0x2565,	//	BOX DRAWINGS DOWN DOUBLE AND HORIZONTAL SINGLE
    0x2566,	//	BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
    0x2567,	//	BOX DRAWINGS UP SINGLE AND HORIZONTAL DOUBLE
    0x2568,	//	BOX DRAWINGS UP DOUBLE AND HORIZONTAL SINGLE
    0x2569,	//	BOX DRAWINGS DOUBLE UP AND HORIZONTAL
    0x256A,	//	BOX DRAWINGS VERTICAL SINGLE AND HORIZONTAL DOUBLE
    0x256B,	//	BOX DRAWINGS VERTICAL DOUBLE AND HORIZONTAL SINGLE
    0x256C,	//	BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
    0x00A9,	//	COPYRIGHT SIGN
    0x044E,	//	CYRILLIC SMALL LETTER YU
    0x0430,	//	CYRILLIC SMALL LETTER A
    0x0431,	//	CYRILLIC SMALL LETTER BE
    0x0446,	//	CYRILLIC SMALL LETTER TSE
    0x0434,	//	CYRILLIC SMALL LETTER DE
    0x0435,	//	CYRILLIC SMALL LETTER IE
    0x0444,	//	CYRILLIC SMALL LETTER EF
    0x0433,	//	CYRILLIC SMALL LETTER GHE
    0x0445,	//	CYRILLIC SMALL LETTER HA
    0x0438,	//	CYRILLIC SMALL LETTER I
    0x0439,	//	CYRILLIC SMALL LETTER SHORT I
    0x043A,	//	CYRILLIC SMALL LETTER KA
    0x043B,	//	CYRILLIC SMALL LETTER EL
    0x043C,	//	CYRILLIC SMALL LETTER EM
    0x043D,	//	CYRILLIC SMALL LETTER EN
    0x043E,	//	CYRILLIC SMALL LETTER O
    0x043F,	//	CYRILLIC SMALL LETTER PE
    0x044F,	//	CYRILLIC SMALL LETTER YA
    0x0440,	//	CYRILLIC SMALL LETTER ER
    0x0441,	//	CYRILLIC SMALL LETTER ES
    0x0442,	//	CYRILLIC SMALL LETTER TE
    0x0443,	//	CYRILLIC SMALL LETTER U
    0x0436,	//	CYRILLIC SMALL LETTER ZHE
    0x0432,	//	CYRILLIC SMALL LETTER VE
    0x044C,	//	CYRILLIC SMALL LETTER SOFT SIGN
    0x044B,	//	CYRILLIC SMALL LETTER YERU
    0x0437,	//	CYRILLIC SMALL LETTER ZE
    0x0448,	//	CYRILLIC SMALL LETTER SHA
    0x044D,	//	CYRILLIC SMALL LETTER E
    0x0449,	//	CYRILLIC SMALL LETTER SHCHA
    0x0447,	//	CYRILLIC SMALL LETTER CHE
    0x044A,	//	CYRILLIC SMALL LETTER HARD SIGN
    0x042E,	//	CYRILLIC CAPITAL LETTER YU
    0x0410,	//	CYRILLIC CAPITAL LETTER A
    0x0411,	//	CYRILLIC CAPITAL LETTER BE
    0x0426,	//	CYRILLIC CAPITAL LETTER TSE
    0x0414,	//	CYRILLIC CAPITAL LETTER DE
    0x0415,	//	CYRILLIC CAPITAL LETTER IE
    0x0424,	//	CYRILLIC CAPITAL LETTER EF
    0x0413,	//	CYRILLIC CAPITAL LETTER GHE
    0x0425,	//	CYRILLIC CAPITAL LETTER HA
    0x0418,	//	CYRILLIC CAPITAL LETTER I
    0x0419,	//	CYRILLIC CAPITAL LETTER SHORT I
    0x041A,	//	CYRILLIC CAPITAL LETTER KA
    0x041B,	//	CYRILLIC CAPITAL LETTER EL
    0x041C,	//	CYRILLIC CAPITAL LETTER EM
    0x041D,	//	CYRILLIC CAPITAL LETTER EN
    0x041E,	//	CYRILLIC CAPITAL LETTER O
    0x041F,	//	CYRILLIC CAPITAL LETTER PE
    0x042F,	//	CYRILLIC CAPITAL LETTER YA
    0x0420,	//	CYRILLIC CAPITAL LETTER ER
    0x0421,	//	CYRILLIC CAPITAL LETTER ES
    0x0422,	//	CYRILLIC CAPITAL LETTER TE
    0x0423,	//	CYRILLIC CAPITAL LETTER U
    0x0416,	//	CYRILLIC CAPITAL LETTER ZHE
    0x0412,	//	CYRILLIC CAPITAL LETTER VE
    0x042C,	//	CYRILLIC CAPITAL LETTER SOFT SIGN
    0x042B,	//	CYRILLIC CAPITAL LETTER YERU
    0x0417,	//	CYRILLIC CAPITAL LETTER ZE
    0x0428,	//	CYRILLIC CAPITAL LETTER SHA
    0x042D,	//	CYRILLIC CAPITAL LETTER E
    0x0429,	//	CYRILLIC CAPITAL LETTER SHCHA
    0x0427,	//	CYRILLIC CAPITAL LETTER CHE
    0x042A	//	CYRILLIC CAPITAL LETTER HARD SIGN
};

static const CharSetCvt::MapEnt toKOI8_R[] = {
{0x00A0, 0x9A},	//	NO-BREAK SPACE
{0x00A9, 0xBF},	//	COPYRIGHT SIGN
{0x00B0, 0x9C},	//	DEGREE SIGN
{0x00B2, 0x9D},	//	SUPERSCRIPT TWO
{0x00B7, 0x9E},	//	MIDDLE DOT
{0x00F7, 0x9F},	//	DIVISION SIGN
{0x0401, 0xB3},	//	CYRILLIC CAPITAL LETTER IO
{0x0410, 0xE1},	//	CYRILLIC CAPITAL LETTER A
{0x0411, 0xE2},	//	CYRILLIC CAPITAL LETTER BE
{0x0412, 0xF7},	//	CYRILLIC CAPITAL LETTER VE
{0x0413, 0xE7},	//	CYRILLIC CAPITAL LETTER GHE
{0x0414, 0xE4},	//	CYRILLIC CAPITAL LETTER DE
{0x0415, 0xE5},	//	CYRILLIC CAPITAL LETTER IE
{0x0416, 0xF6},	//	CYRILLIC CAPITAL LETTER ZHE
{0x0417, 0xFA},	//	CYRILLIC CAPITAL LETTER ZE
{0x0418, 0xE9},	//	CYRILLIC CAPITAL LETTER I
{0x0419, 0xEA},	//	CYRILLIC CAPITAL LETTER SHORT I
{0x041A, 0xEB},	//	CYRILLIC CAPITAL LETTER KA
{0x041B, 0xEC},	//	CYRILLIC CAPITAL LETTER EL
{0x041C, 0xED},	//	CYRILLIC CAPITAL LETTER EM
{0x041D, 0xEE},	//	CYRILLIC CAPITAL LETTER EN
{0x041E, 0xEF},	//	CYRILLIC CAPITAL LETTER O
{0x041F, 0xF0},	//	CYRILLIC CAPITAL LETTER PE
{0x0420, 0xF2},	//	CYRILLIC CAPITAL LETTER ER
{0x0421, 0xF3},	//	CYRILLIC CAPITAL LETTER ES
{0x0422, 0xF4},	//	CYRILLIC CAPITAL LETTER TE
{0x0423, 0xF5},	//	CYRILLIC CAPITAL LETTER U
{0x0424, 0xE6},	//	CYRILLIC CAPITAL LETTER EF
{0x0425, 0xE8},	//	CYRILLIC CAPITAL LETTER HA
{0x0426, 0xE3},	//	CYRILLIC CAPITAL LETTER TSE
{0x0427, 0xFE},	//	CYRILLIC CAPITAL LETTER CHE
{0x0428, 0xFB},	//	CYRILLIC CAPITAL LETTER SHA
{0x0429, 0xFD},	//	CYRILLIC CAPITAL LETTER SHCHA
{0x042A, 0xFF},	//	CYRILLIC CAPITAL LETTER HARD SIGN
{0x042B, 0xF9},	//	CYRILLIC CAPITAL LETTER YERU
{0x042C, 0xF8},	//	CYRILLIC CAPITAL LETTER SOFT SIGN
{0x042D, 0xFC},	//	CYRILLIC CAPITAL LETTER E
{0x042E, 0xE0},	//	CYRILLIC CAPITAL LETTER YU
{0x042F, 0xF1},	//	CYRILLIC CAPITAL LETTER YA
{0x0430, 0xC1},	//	CYRILLIC SMALL LETTER A
{0x0431, 0xC2},	//	CYRILLIC SMALL LETTER BE
{0x0432, 0xD7},	//	CYRILLIC SMALL LETTER VE
{0x0433, 0xC7},	//	CYRILLIC SMALL LETTER GHE
{0x0434, 0xC4},	//	CYRILLIC SMALL LETTER DE
{0x0435, 0xC5},	//	CYRILLIC SMALL LETTER IE
{0x0436, 0xD6},	//	CYRILLIC SMALL LETTER ZHE
{0x0437, 0xDA},	//	CYRILLIC SMALL LETTER ZE
{0x0438, 0xC9},	//	CYRILLIC SMALL LETTER I
{0x0439, 0xCA},	//	CYRILLIC SMALL LETTER SHORT I
{0x043A, 0xCB},	//	CYRILLIC SMALL LETTER KA
{0x043B, 0xCC},	//	CYRILLIC SMALL LETTER EL
{0x043C, 0xCD},	//	CYRILLIC SMALL LETTER EM
{0x043D, 0xCE},	//	CYRILLIC SMALL LETTER EN
{0x043E, 0xCF},	//	CYRILLIC SMALL LETTER O
{0x043F, 0xD0},	//	CYRILLIC SMALL LETTER PE
{0x0440, 0xD2},	//	CYRILLIC SMALL LETTER ER
{0x0441, 0xD3},	//	CYRILLIC SMALL LETTER ES
{0x0442, 0xD4},	//	CYRILLIC SMALL LETTER TE
{0x0443, 0xD5},	//	CYRILLIC SMALL LETTER U
{0x0444, 0xC6},	//	CYRILLIC SMALL LETTER EF
{0x0445, 0xC8},	//	CYRILLIC SMALL LETTER HA
{0x0446, 0xC3},	//	CYRILLIC SMALL LETTER TSE
{0x0447, 0xDE},	//	CYRILLIC SMALL LETTER CHE
{0x0448, 0xDB},	//	CYRILLIC SMALL LETTER SHA
{0x0449, 0xDD},	//	CYRILLIC SMALL LETTER SHCHA
{0x044A, 0xDF},	//	CYRILLIC SMALL LETTER HARD SIGN
{0x044B, 0xD9},	//	CYRILLIC SMALL LETTER YERU
{0x044C, 0xD8},	//	CYRILLIC SMALL LETTER SOFT SIGN
{0x044D, 0xDC},	//	CYRILLIC SMALL LETTER E
{0x044E, 0xC0},	//	CYRILLIC SMALL LETTER YU
{0x044F, 0xD1},	//	CYRILLIC SMALL LETTER YA
{0x0451, 0xA3},	//	CYRILLIC SMALL LETTER IO
{0x2219, 0x95},	//	BULLET OPERATOR
{0x221A, 0x96},	//	SQUARE ROOT
{0x2248, 0x97},	//	ALMOST EQUAL TO
{0x2264, 0x98},	//	LESS-THAN OR EQUAL TO
{0x2265, 0x99},	//	GREATER-THAN OR EQUAL TO
{0x2320, 0x93},	//	TOP HALF INTEGRAL
{0x2321, 0x9B},	//	BOTTOM HALF INTEGRAL
{0x2500, 0x80},	//	BOX DRAWINGS LIGHT HORIZONTAL
{0x2502, 0x81},	//	BOX DRAWINGS LIGHT VERTICAL
{0x250C, 0x82},	//	BOX DRAWINGS LIGHT DOWN AND RIGHT
{0x2510, 0x83},	//	BOX DRAWINGS LIGHT DOWN AND LEFT
{0x2514, 0x84},	//	BOX DRAWINGS LIGHT UP AND RIGHT
{0x2518, 0x85},	//	BOX DRAWINGS LIGHT UP AND LEFT
{0x251C, 0x86},	//	BOX DRAWINGS LIGHT VERTICAL AND RIGHT
{0x2524, 0x87},	//	BOX DRAWINGS LIGHT VERTICAL AND LEFT
{0x252C, 0x88},	//	BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
{0x2534, 0x89},	//	BOX DRAWINGS LIGHT UP AND HORIZONTAL
{0x253C, 0x8A},	//	BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
{0x2550, 0xA0},	//	BOX DRAWINGS DOUBLE HORIZONTAL
{0x2551, 0xA1},	//	BOX DRAWINGS DOUBLE VERTICAL
{0x2552, 0xA2},	//	BOX DRAWINGS DOWN SINGLE AND RIGHT DOUBLE
{0x2553, 0xA4},	//	BOX DRAWINGS DOWN DOUBLE AND RIGHT SINGLE
{0x2554, 0xA5},	//	BOX DRAWINGS DOUBLE DOWN AND RIGHT
{0x2555, 0xA6},	//	BOX DRAWINGS DOWN SINGLE AND LEFT DOUBLE
{0x2556, 0xA7},	//	BOX DRAWINGS DOWN DOUBLE AND LEFT SINGLE
{0x2557, 0xA8},	//	BOX DRAWINGS DOUBLE DOWN AND LEFT
{0x2558, 0xA9},	//	BOX DRAWINGS UP SINGLE AND RIGHT DOUBLE
{0x2559, 0xAA},	//	BOX DRAWINGS UP DOUBLE AND RIGHT SINGLE
{0x255A, 0xAB},	//	BOX DRAWINGS DOUBLE UP AND RIGHT
{0x255B, 0xAC},	//	BOX DRAWINGS UP SINGLE AND LEFT DOUBLE
{0x255C, 0xAD},	//	BOX DRAWINGS UP DOUBLE AND LEFT SINGLE
{0x255D, 0xAE},	//	BOX DRAWINGS DOUBLE UP AND LEFT
{0x255E, 0xAF},	//	BOX DRAWINGS VERTICAL SINGLE AND RIGHT DOUBLE
{0x255F, 0xB0},	//	BOX DRAWINGS VERTICAL DOUBLE AND RIGHT SINGLE
{0x2560, 0xB1},	//	BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
{0x2561, 0xB2},	//	BOX DRAWINGS VERTICAL SINGLE AND LEFT DOUBLE
{0x2562, 0xB4},	//	BOX DRAWINGS VERTICAL DOUBLE AND LEFT SINGLE
{0x2563, 0xB5},	//	BOX DRAWINGS DOUBLE VERTICAL AND LEFT
{0x2564, 0xB6},	//	BOX DRAWINGS DOWN SINGLE AND HORIZONTAL DOUBLE
{0x2565, 0xB7},	//	BOX DRAWINGS DOWN DOUBLE AND HORIZONTAL SINGLE
{0x2566, 0xB8},	//	BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
{0x2567, 0xB9},	//	BOX DRAWINGS UP SINGLE AND HORIZONTAL DOUBLE
{0x2568, 0xBA},	//	BOX DRAWINGS UP DOUBLE AND HORIZONTAL SINGLE
{0x2569, 0xBB},	//	BOX DRAWINGS DOUBLE UP AND HORIZONTAL
{0x256A, 0xBC},	//	BOX DRAWINGS VERTICAL SINGLE AND HORIZONTAL DOUBLE
{0x256B, 0xBD},	//	BOX DRAWINGS VERTICAL DOUBLE AND HORIZONTAL SINGLE
{0x256C, 0xBE},	//	BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
{0x2580, 0x8B},	//	UPPER HALF BLOCK
{0x2584, 0x8C},	//	LOWER HALF BLOCK
{0x2588, 0x8D},	//	FULL BLOCK
{0x258C, 0x8E},	//	LEFT HALF BLOCK
{0x2590, 0x8F},	//	RIGHT HALF BLOCK
{0x2591, 0x90},	//	LIGHT SHADE
{0x2592, 0x91},	//	MEDIUM SHADE
{0x2593, 0x92},	//	DARK SHADE
{0x25A0, 0x94}	//	BLACK SQUARE
};

static const unsigned short fromCP1251[] = {
    0x0402,	// CYRILLIC CAPITAL LETTER DJE
    0x0403,	// CYRILLIC CAPITAL LETTER GJE
    0x201A,	// SINGLE LOW-9 QUOTATION MARK
    0x0453,	// CYRILLIC SMALL LETTER GJE
    0x201E,	// DOUBLE LOW-9 QUOTATION MARK
    0x2026,	// HORIZONTAL ELLIPSIS
    0x2020,	// DAGGER
    0x2021,	// DOUBLE DAGGER
    0x20AC,	// EURO SIGN
    0x2030,	// PER MILLE SIGN
    0x0409,	// CYRILLIC CAPITAL LETTER LJE
    0x2039,	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
    0x040A,	// CYRILLIC CAPITAL LETTER NJE
    0x040C,	// CYRILLIC CAPITAL LETTER KJE
    0x040B,	// CYRILLIC CAPITAL LETTER TSHE
    0x040F,	// CYRILLIC CAPITAL LETTER DZHE
    0x0452,	// CYRILLIC SMALL LETTER DJE
    0x2018,	// LEFT SINGLE QUOTATION MARK
    0x2019,	// RIGHT SINGLE QUOTATION MARK
    0x201C,	// LEFT DOUBLE QUOTATION MARK
    0x201D,	// RIGHT DOUBLE QUOTATION MARK
    0x2022,	// BULLET
    0x2013,	// EN DASH
    0x2014,	// EM DASH
    0xfffd, // #UNDEFINED
    0x2122,	// TRADE MARK SIGN
    0x0459,	// CYRILLIC SMALL LETTER LJE
    0x203A,	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
    0x045A,	// CYRILLIC SMALL LETTER NJE
    0x045C,	// CYRILLIC SMALL LETTER KJE
    0x045B,	// CYRILLIC SMALL LETTER TSHE
    0x045F,	// CYRILLIC SMALL LETTER DZHE
    0x00A0,	// NO-BREAK SPACE
    0x040E,	// CYRILLIC CAPITAL LETTER SHORT U
    0x045E,	// CYRILLIC SMALL LETTER SHORT U
    0x0408,	// CYRILLIC CAPITAL LETTER JE
    0x00A4,	// CURRENCY SIGN
    0x0490,	// CYRILLIC CAPITAL LETTER GHE WITH UPTURN
    0x00A6,	// BROKEN BAR
    0x00A7,	// SECTION SIGN
    0x0401,	// CYRILLIC CAPITAL LETTER IO
    0x00A9,	// COPYRIGHT SIGN
    0x0404,	// CYRILLIC CAPITAL LETTER UKRAINIAN IE
    0x00AB,	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00AC,	// NOT SIGN
    0x00AD,	// SOFT HYPHEN
    0x00AE,	// REGISTERED SIGN
    0x0407,	// CYRILLIC CAPITAL LETTER YI
    0x00B0,	// DEGREE SIGN
    0x00B1,	// PLUS-MINUS SIGN
    0x0406,	// CYRILLIC CAPITAL LETTER BYELORUSSIAN-UKRAINIAN I
    0x0456,	// CYRILLIC SMALL LETTER BYELORUSSIAN-UKRAINIAN I
    0x0491,	// CYRILLIC SMALL LETTER GHE WITH UPTURN
    0x00B5,	// MICRO SIGN
    0x00B6,	// PILCROW SIGN
    0x00B7,	// MIDDLE DOT
    0x0451,	// CYRILLIC SMALL LETTER IO
    0x2116,	// NUMERO SIGN
    0x0454,	// CYRILLIC SMALL LETTER UKRAINIAN IE
    0x00BB,	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x0458,	// CYRILLIC SMALL LETTER JE
    0x0405,	// CYRILLIC CAPITAL LETTER DZE
    0x0455,	// CYRILLIC SMALL LETTER DZE
    0x0457,	// CYRILLIC SMALL LETTER YI
    0x0410,	// CYRILLIC CAPITAL LETTER A
    0x0411,	// CYRILLIC CAPITAL LETTER BE
    0x0412,	// CYRILLIC CAPITAL LETTER VE
    0x0413,	// CYRILLIC CAPITAL LETTER GHE
    0x0414,	// CYRILLIC CAPITAL LETTER DE
    0x0415,	// CYRILLIC CAPITAL LETTER IE
    0x0416,	// CYRILLIC CAPITAL LETTER ZHE
    0x0417,	// CYRILLIC CAPITAL LETTER ZE
    0x0418,	// CYRILLIC CAPITAL LETTER I
    0x0419,	// CYRILLIC CAPITAL LETTER SHORT I
    0x041A,	// CYRILLIC CAPITAL LETTER KA
    0x041B,	// CYRILLIC CAPITAL LETTER EL
    0x041C,	// CYRILLIC CAPITAL LETTER EM
    0x041D,	// CYRILLIC CAPITAL LETTER EN
    0x041E,	// CYRILLIC CAPITAL LETTER O
    0x041F,	// CYRILLIC CAPITAL LETTER PE
    0x0420,	// CYRILLIC CAPITAL LETTER ER
    0x0421,	// CYRILLIC CAPITAL LETTER ES
    0x0422,	// CYRILLIC CAPITAL LETTER TE
    0x0423,	// CYRILLIC CAPITAL LETTER U
    0x0424,	// CYRILLIC CAPITAL LETTER EF
    0x0425,	// CYRILLIC CAPITAL LETTER HA
    0x0426,	// CYRILLIC CAPITAL LETTER TSE
    0x0427,	// CYRILLIC CAPITAL LETTER CHE
    0x0428,	// CYRILLIC CAPITAL LETTER SHA
    0x0429,	// CYRILLIC CAPITAL LETTER SHCHA
    0x042A,	// CYRILLIC CAPITAL LETTER HARD SIGN
    0x042B,	// CYRILLIC CAPITAL LETTER YERU
    0x042C,	// CYRILLIC CAPITAL LETTER SOFT SIGN
    0x042D,	// CYRILLIC CAPITAL LETTER E
    0x042E,	// CYRILLIC CAPITAL LETTER YU
    0x042F,	// CYRILLIC CAPITAL LETTER YA
    0x0430,	// CYRILLIC SMALL LETTER A
    0x0431,	// CYRILLIC SMALL LETTER BE
    0x0432,	// CYRILLIC SMALL LETTER VE
    0x0433,	// CYRILLIC SMALL LETTER GHE
    0x0434,	// CYRILLIC SMALL LETTER DE
    0x0435,	// CYRILLIC SMALL LETTER IE
    0x0436,	// CYRILLIC SMALL LETTER ZHE
    0x0437,	// CYRILLIC SMALL LETTER ZE
    0x0438,	// CYRILLIC SMALL LETTER I
    0x0439,	// CYRILLIC SMALL LETTER SHORT I
    0x043A,	// CYRILLIC SMALL LETTER KA
    0x043B,	// CYRILLIC SMALL LETTER EL
    0x043C,	// CYRILLIC SMALL LETTER EM
    0x043D,	// CYRILLIC SMALL LETTER EN
    0x043E,	// CYRILLIC SMALL LETTER O
    0x043F,	// CYRILLIC SMALL LETTER PE
    0x0440,	// CYRILLIC SMALL LETTER ER
    0x0441,	// CYRILLIC SMALL LETTER ES
    0x0442,	// CYRILLIC SMALL LETTER TE
    0x0443,	// CYRILLIC SMALL LETTER U
    0x0444,	// CYRILLIC SMALL LETTER EF
    0x0445,	// CYRILLIC SMALL LETTER HA
    0x0446,	// CYRILLIC SMALL LETTER TSE
    0x0447,	// CYRILLIC SMALL LETTER CHE
    0x0448,	// CYRILLIC SMALL LETTER SHA
    0x0449,	// CYRILLIC SMALL LETTER SHCHA
    0x044A,	// CYRILLIC SMALL LETTER HARD SIGN
    0x044B,	// CYRILLIC SMALL LETTER YERU
    0x044C,	// CYRILLIC SMALL LETTER SOFT SIGN
    0x044D,	// CYRILLIC SMALL LETTER E
    0x044E,	// CYRILLIC SMALL LETTER YU
    0x044F	// CYRILLIC SMALL LETTER YA
};

static const CharSetCvt::MapEnt toCP1251[] = {
{0x00A0, 0xA0},	// NO-BREAK SPACE
{0x00A4, 0xA4},	// CURRENCY SIGN
{0x00A6, 0xA6},	// BROKEN BAR
{0x00A7, 0xA7},	// SECTION SIGN
{0x00A9, 0xA9},	// COPYRIGHT SIGN
{0x00AB, 0xAB},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xAC},	// NOT SIGN
{0x00AD, 0xAD},	// SOFT HYPHEN
{0x00AE, 0xAE},	// REGISTERED SIGN
{0x00B0, 0xB0},	// DEGREE SIGN
{0x00B1, 0xB1},	// PLUS-MINUS SIGN
{0x00B5, 0xB5},	// MICRO SIGN
{0x00B6, 0xB6},	// PILCROW SIGN
{0x00B7, 0xB7},	// MIDDLE DOT
{0x00BB, 0xBB},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x0401, 0xA8},	// CYRILLIC CAPITAL LETTER IO
{0x0402, 0x80},	// CYRILLIC CAPITAL LETTER DJE
{0x0403, 0x81},	// CYRILLIC CAPITAL LETTER GJE
{0x0404, 0xAA},	// CYRILLIC CAPITAL LETTER UKRAINIAN IE
{0x0405, 0xBD},	// CYRILLIC CAPITAL LETTER DZE
{0x0406, 0xB2},	// CYRILLIC CAPITAL LETTER BYELORUSSIAN-UKRAINIAN I
{0x0407, 0xAF},	// CYRILLIC CAPITAL LETTER YI
{0x0408, 0xA3},	// CYRILLIC CAPITAL LETTER JE
{0x0409, 0x8A},	// CYRILLIC CAPITAL LETTER LJE
{0x040A, 0x8C},	// CYRILLIC CAPITAL LETTER NJE
{0x040B, 0x8E},	// CYRILLIC CAPITAL LETTER TSHE
{0x040C, 0x8D},	// CYRILLIC CAPITAL LETTER KJE
{0x040E, 0xA1},	// CYRILLIC CAPITAL LETTER SHORT U
{0x040F, 0x8F},	// CYRILLIC CAPITAL LETTER DZHE
{0x0410, 0xC0},	// CYRILLIC CAPITAL LETTER A
{0x0411, 0xC1},	// CYRILLIC CAPITAL LETTER BE
{0x0412, 0xC2},	// CYRILLIC CAPITAL LETTER VE
{0x0413, 0xC3},	// CYRILLIC CAPITAL LETTER GHE
{0x0414, 0xC4},	// CYRILLIC CAPITAL LETTER DE
{0x0415, 0xC5},	// CYRILLIC CAPITAL LETTER IE
{0x0416, 0xC6},	// CYRILLIC CAPITAL LETTER ZHE
{0x0417, 0xC7},	// CYRILLIC CAPITAL LETTER ZE
{0x0418, 0xC8},	// CYRILLIC CAPITAL LETTER I
{0x0419, 0xC9},	// CYRILLIC CAPITAL LETTER SHORT I
{0x041A, 0xCA},	// CYRILLIC CAPITAL LETTER KA
{0x041B, 0xCB},	// CYRILLIC CAPITAL LETTER EL
{0x041C, 0xCC},	// CYRILLIC CAPITAL LETTER EM
{0x041D, 0xCD},	// CYRILLIC CAPITAL LETTER EN
{0x041E, 0xCE},	// CYRILLIC CAPITAL LETTER O
{0x041F, 0xCF},	// CYRILLIC CAPITAL LETTER PE
{0x0420, 0xD0},	// CYRILLIC CAPITAL LETTER ER
{0x0421, 0xD1},	// CYRILLIC CAPITAL LETTER ES
{0x0422, 0xD2},	// CYRILLIC CAPITAL LETTER TE
{0x0423, 0xD3},	// CYRILLIC CAPITAL LETTER U
{0x0424, 0xD4},	// CYRILLIC CAPITAL LETTER EF
{0x0425, 0xD5},	// CYRILLIC CAPITAL LETTER HA
{0x0426, 0xD6},	// CYRILLIC CAPITAL LETTER TSE
{0x0427, 0xD7},	// CYRILLIC CAPITAL LETTER CHE
{0x0428, 0xD8},	// CYRILLIC CAPITAL LETTER SHA
{0x0429, 0xD9},	// CYRILLIC CAPITAL LETTER SHCHA
{0x042A, 0xDA},	// CYRILLIC CAPITAL LETTER HARD SIGN
{0x042B, 0xDB},	// CYRILLIC CAPITAL LETTER YERU
{0x042C, 0xDC},	// CYRILLIC CAPITAL LETTER SOFT SIGN
{0x042D, 0xDD},	// CYRILLIC CAPITAL LETTER E
{0x042E, 0xDE},	// CYRILLIC CAPITAL LETTER YU
{0x042F, 0xDF},	// CYRILLIC CAPITAL LETTER YA
{0x0430, 0xE0},	// CYRILLIC SMALL LETTER A
{0x0431, 0xE1},	// CYRILLIC SMALL LETTER BE
{0x0432, 0xE2},	// CYRILLIC SMALL LETTER VE
{0x0433, 0xE3},	// CYRILLIC SMALL LETTER GHE
{0x0434, 0xE4},	// CYRILLIC SMALL LETTER DE
{0x0435, 0xE5},	// CYRILLIC SMALL LETTER IE
{0x0436, 0xE6},	// CYRILLIC SMALL LETTER ZHE
{0x0437, 0xE7},	// CYRILLIC SMALL LETTER ZE
{0x0438, 0xE8},	// CYRILLIC SMALL LETTER I
{0x0439, 0xE9},	// CYRILLIC SMALL LETTER SHORT I
{0x043A, 0xEA},	// CYRILLIC SMALL LETTER KA
{0x043B, 0xEB},	// CYRILLIC SMALL LETTER EL
{0x043C, 0xEC},	// CYRILLIC SMALL LETTER EM
{0x043D, 0xED},	// CYRILLIC SMALL LETTER EN
{0x043E, 0xEE},	// CYRILLIC SMALL LETTER O
{0x043F, 0xEF},	// CYRILLIC SMALL LETTER PE
{0x0440, 0xF0},	// CYRILLIC SMALL LETTER ER
{0x0441, 0xF1},	// CYRILLIC SMALL LETTER ES
{0x0442, 0xF2},	// CYRILLIC SMALL LETTER TE
{0x0443, 0xF3},	// CYRILLIC SMALL LETTER U
{0x0444, 0xF4},	// CYRILLIC SMALL LETTER EF
{0x0445, 0xF5},	// CYRILLIC SMALL LETTER HA
{0x0446, 0xF6},	// CYRILLIC SMALL LETTER TSE
{0x0447, 0xF7},	// CYRILLIC SMALL LETTER CHE
{0x0448, 0xF8},	// CYRILLIC SMALL LETTER SHA
{0x0449, 0xF9},	// CYRILLIC SMALL LETTER SHCHA
{0x044A, 0xFA},	// CYRILLIC SMALL LETTER HARD SIGN
{0x044B, 0xFB},	// CYRILLIC SMALL LETTER YERU
{0x044C, 0xFC},	// CYRILLIC SMALL LETTER SOFT SIGN
{0x044D, 0xFD},	// CYRILLIC SMALL LETTER E
{0x044E, 0xFE},	// CYRILLIC SMALL LETTER YU
{0x044F, 0xFF},	// CYRILLIC SMALL LETTER YA
{0x0451, 0xB8},	// CYRILLIC SMALL LETTER IO
{0x0452, 0x90},	// CYRILLIC SMALL LETTER DJE
{0x0453, 0x83},	// CYRILLIC SMALL LETTER GJE
{0x0454, 0xBA},	// CYRILLIC SMALL LETTER UKRAINIAN IE
{0x0455, 0xBE},	// CYRILLIC SMALL LETTER DZE
{0x0456, 0xB3},	// CYRILLIC SMALL LETTER BYELORUSSIAN-UKRAINIAN I
{0x0457, 0xBF},	// CYRILLIC SMALL LETTER YI
{0x0458, 0xBC},	// CYRILLIC SMALL LETTER JE
{0x0459, 0x9A},	// CYRILLIC SMALL LETTER LJE
{0x045A, 0x9C},	// CYRILLIC SMALL LETTER NJE
{0x045B, 0x9E},	// CYRILLIC SMALL LETTER TSHE
{0x045C, 0x9D},	// CYRILLIC SMALL LETTER KJE
{0x045E, 0xA2},	// CYRILLIC SMALL LETTER SHORT U
{0x045F, 0x9F},	// CYRILLIC SMALL LETTER DZHE
{0x0490, 0xA5},	// CYRILLIC CAPITAL LETTER GHE WITH UPTURN
{0x0491, 0xB4},	// CYRILLIC SMALL LETTER GHE WITH UPTURN
{0x2013, 0x96},	// EN DASH
{0x2014, 0x97},	// EM DASH
{0x2018, 0x91},	// LEFT SINGLE QUOTATION MARK
{0x2019, 0x92},	// RIGHT SINGLE QUOTATION MARK
{0x201A, 0x82},	// SINGLE LOW-9 QUOTATION MARK
{0x201C, 0x93},	// LEFT DOUBLE QUOTATION MARK
{0x201D, 0x94},	// RIGHT DOUBLE QUOTATION MARK
{0x201E, 0x84},	// DOUBLE LOW-9 QUOTATION MARK
{0x2020, 0x86},	// DAGGER
{0x2021, 0x87},	// DOUBLE DAGGER
{0x2022, 0x95},	// BULLET
{0x2026, 0x85},	// HORIZONTAL ELLIPSIS
{0x2030, 0x89},	// PER MILLE SIGN
{0x2039, 0x8B},	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
{0x203A, 0x9B},	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
{0x20AC, 0x88},	// EURO SIGN
{0x2116, 0xB9},	// NUMERO SIGN
{0x2122, 0x99}	// TRADE MARK SIGN
};

static const unsigned short fromCP1252[] = {
	0x20AC,	//	0x80
	0xfffd,	//	0x81
	0x201A,	//	0x82
	0x0192,	//	0x83
	0x201E,	//	0x84
	0x2026,	//	0x85
	0x2020,	//	0x86
	0x2021,	//	0x87
	0x02C6,	//	0x88
	0x2030,	//	0x89
	0x0160,	//	0x8A
	0x2039,	//	0x8B
	0x0152,	//	0x8C
	0xfffd,	//	0x8D
	0x017D,	//	0x8E
	0xfffd,	//	0x8F
	0xfffd,	//	0x90
	0x2018,	//	0x91
	0x2019,	//	0x92
	0x201C,	//	0x93
	0x201D,	//	0x94
	0x2022,	//	0x95
	0x2013,	//	0x96
	0x2014,	//	0x97
	0x02DC,	//	0x98
	0x2122,	//	0x99
	0x0161,	//	0x9A
	0x203A,	//	0x9B
	0x0153,	//	0x9C
	0xfffd,	//	0x9D
	0x017E,	//	0x9E
	0x0178,	//	0x9F
	0x00A0,
	0x00A1,
	0x00A2,
	0x00A3,
	0x00A4,
	0x00A5,
	0x00A6,
	0x00A7,
	0x00A8,
	0x00A9,
	0x00AA,
	0x00AB,
	0x00AC,
	0x00AD,
	0x00AE,
	0x00AF,
	0x00B0,
	0x00B1,
	0x00B2,
	0x00B3,
	0x00B4,
	0x00B5,
	0x00B6,
	0x00B7,
	0x00B8,
	0x00B9,
	0x00BA,
	0x00BB,
	0x00BC,
	0x00BD,
	0x00BE,
	0x00BF,
	0x00C0,
	0x00C1,
	0x00C2,
	0x00C3,
	0x00C4,
	0x00C5,
	0x00C6,
	0x00C7,
	0x00C8,
	0x00C9,
	0x00CA,
	0x00CB,
	0x00CC,
	0x00CD,
	0x00CE,
	0x00CF,
	0x00D0,
	0x00D1,
	0x00D2,
	0x00D3,
	0x00D4,
	0x00D5,
	0x00D6,
	0x00D7,
	0x00D8,
	0x00D9,
	0x00DA,
	0x00DB,
	0x00DC,
	0x00DD,
	0x00DE,
	0x00DF,
	0x00E0,
	0x00E1,
	0x00E2,
	0x00E3,
	0x00E4,
	0x00E5,
	0x00E6,
	0x00E7,
	0x00E8,
	0x00E9,
	0x00EA,
	0x00EB,
	0x00EC,
	0x00ED,
	0x00EE,
	0x00EF,
	0x00F0,
	0x00F1,
	0x00F2,
	0x00F3,
	0x00F4,
	0x00F5,
	0x00F6,
	0x00F7,
	0x00F8,
	0x00F9,
	0x00FA,
	0x00FB,
	0x00FC,
	0x00FD,
	0x00FE,
	0x00FF
};

static const CharSetCvt::MapEnt toCP1252[] = {
{0x00A0, 0x00A0},
{0x00A1, 0x00A1},
{0x00A2, 0x00A2},
{0x00A3, 0x00A3},
{0x00A4, 0x00A4},
{0x00A5, 0x00A5},
{0x00A6, 0x00A6},
{0x00A7, 0x00A7},
{0x00A8, 0x00A8},
{0x00A9, 0x00A9},
{0x00AA, 0x00AA},
{0x00AB, 0x00AB},
{0x00AC, 0x00AC},
{0x00AD, 0x00AD},
{0x00AE, 0x00AE},
{0x00AF, 0x00AF},
{0x00B0, 0x00B0},
{0x00B1, 0x00B1},
{0x00B2, 0x00B2},
{0x00B3, 0x00B3},
{0x00B4, 0x00B4},
{0x00B5, 0x00B5},
{0x00B6, 0x00B6},
{0x00B7, 0x00B7},
{0x00B8, 0x00B8},
{0x00B9, 0x00B9},
{0x00BA, 0x00BA},
{0x00BB, 0x00BB},
{0x00BC, 0x00BC},
{0x00BD, 0x00BD},
{0x00BE, 0x00BE},
{0x00BF, 0x00BF},
{0x00C0, 0x00C0},
{0x00C1, 0x00C1},
{0x00C2, 0x00C2},
{0x00C3, 0x00C3},
{0x00C4, 0x00C4},
{0x00C5, 0x00C5},
{0x00C6, 0x00C6},
{0x00C7, 0x00C7},
{0x00C8, 0x00C8},
{0x00C9, 0x00C9},
{0x00CA, 0x00CA},
{0x00CB, 0x00CB},
{0x00CC, 0x00CC},
{0x00CD, 0x00CD},
{0x00CE, 0x00CE},
{0x00CF, 0x00CF},
{0x00D0, 0x00D0},
{0x00D1, 0x00D1},
{0x00D2, 0x00D2},
{0x00D3, 0x00D3},
{0x00D4, 0x00D4},
{0x00D5, 0x00D5},
{0x00D6, 0x00D6},
{0x00D7, 0x00D7},
{0x00D8, 0x00D8},
{0x00D9, 0x00D9},
{0x00DA, 0x00DA},
{0x00DB, 0x00DB},
{0x00DC, 0x00DC},
{0x00DD, 0x00DD},
{0x00DE, 0x00DE},
{0x00DF, 0x00DF},
{0x00E0, 0x00E0},
{0x00E1, 0x00E1},
{0x00E2, 0x00E2},
{0x00E3, 0x00E3},
{0x00E4, 0x00E4},
{0x00E5, 0x00E5},
{0x00E6, 0x00E6},
{0x00E7, 0x00E7},
{0x00E8, 0x00E8},
{0x00E9, 0x00E9},
{0x00EA, 0x00EA},
{0x00EB, 0x00EB},
{0x00EC, 0x00EC},
{0x00ED, 0x00ED},
{0x00EE, 0x00EE},
{0x00EF, 0x00EF},
{0x00F0, 0x00F0},
{0x00F1, 0x00F1},
{0x00F2, 0x00F2},
{0x00F3, 0x00F3},
{0x00F4, 0x00F4},
{0x00F5, 0x00F5},
{0x00F6, 0x00F6},
{0x00F7, 0x00F7},
{0x00F8, 0x00F8},
{0x00F9, 0x00F9},
{0x00FA, 0x00FA},
{0x00FB, 0x00FB},
{0x00FC, 0x00FC},
{0x00FD, 0x00FD},
{0x00FE, 0x00FE},
{0x00FF, 0x00FF},
{0x0152, 0x8C},
{0x0153, 0x9C},
{0x0160, 0x8A},
{0x0161, 0x9A},
{0x0178, 0x9F},
{0x017D, 0x8E},
{0x017E, 0x9E},
{0x0192, 0x83},
{0x02C6, 0x88},
{0x02DC, 0x98},
{0x2013, 0x96},
{0x2014, 0x97},
{0x2018, 0x91},
{0x2019, 0x92},
{0x201A, 0x82},
{0x201C, 0x93},
{0x201D, 0x94},
{0x201E, 0x84},
{0x2020, 0x86},
{0x2021, 0x87},
{0x2022, 0x95},
{0x2026, 0x85},
{0x2030, 0x89},
{0x2039, 0x8B},
{0x203A, 0x9B},
{0x20AC, 0x80},
{0x2122, 0x99}
};

static const unsigned short fromCP1253[] = {
    0x20AC,	// EURO SIGN
    0xfffd, // #UNDEFINED
    0x201A,	// SINGLE LOW-9 QUOTATION MARK
    0x0192,	// LATIN SMALL LETTER F WITH HOOK
    0x201E,	// DOUBLE LOW-9 QUOTATION MARK
    0x2026,	// HORIZONTAL ELLIPSIS
    0x2020,	// DAGGER
    0x2021,	// DOUBLE DAGGER
    0xfffd, // #UNDEFINED
    0x2030,	// PER MILLE SIGN
    0xfffd, // #UNDEFINED
    0x2039,	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0x2018,	// LEFT SINGLE QUOTATION MARK
    0x2019,	// RIGHT SINGLE QUOTATION MARK
    0x201C,	// LEFT DOUBLE QUOTATION MARK
    0x201D,	// RIGHT DOUBLE QUOTATION MARK
    0x2022,	// BULLET
    0x2013,	// EN DASH
    0x2014,	// EM DASH
    0xfffd, // #UNDEFINED
    0x2122,	// TRADE MARK SIGN
    0xfffd, // #UNDEFINED
    0x203A,	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0xfffd, // #UNDEFINED
    0x00A0,	// NO-BREAK SPACE
    0x0385,	// GREEK DIALYTIKA TONOS
    0x0386,	// GREEK CAPITAL LETTER ALPHA WITH TONOS
    0x00A3,	// POUND SIGN
    0x00A4,	// CURRENCY SIGN
    0x00A5,	// YEN SIGN
    0x00A6,	// BROKEN BAR
    0x00A7,	// SECTION SIGN
    0x00A8,	// DIAERESIS
    0x00A9,	// COPYRIGHT SIGN
    0xfffd, // #UNDEFINED
    0x00AB,	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00AC,	// NOT SIGN
    0x00AD,	// SOFT HYPHEN
    0x00AE,	// REGISTERED SIGN
    0x2015,	// HORIZONTAL BAR
    0x00B0,	// DEGREE SIGN
    0x00B1,	// PLUS-MINUS SIGN
    0x00B2,	// SUPERSCRIPT TWO
    0x00B3,	// SUPERSCRIPT THREE
    0x0384,	// GREEK TONOS
    0x00B5,	// MICRO SIGN
    0x00B6,	// PILCROW SIGN
    0x00B7,	// MIDDLE DOT
    0x0388,	// GREEK CAPITAL LETTER EPSILON WITH TONOS
    0x0389,	// GREEK CAPITAL LETTER ETA WITH TONOS
    0x038A,	// GREEK CAPITAL LETTER IOTA WITH TONOS
    0x00BB,	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x038C,	// GREEK CAPITAL LETTER OMICRON WITH TONOS
    0x00BD,	// VULGAR FRACTION ONE HALF
    0x038E,	// GREEK CAPITAL LETTER UPSILON WITH TONOS
    0x038F,	// GREEK CAPITAL LETTER OMEGA WITH TONOS
    0x0390,	// GREEK SMALL LETTER IOTA WITH DIALYTIKA AND TONOS
    0x0391,	// GREEK CAPITAL LETTER ALPHA
    0x0392,	// GREEK CAPITAL LETTER BETA
    0x0393,	// GREEK CAPITAL LETTER GAMMA
    0x0394,	// GREEK CAPITAL LETTER DELTA
    0x0395,	// GREEK CAPITAL LETTER EPSILON
    0x0396,	// GREEK CAPITAL LETTER ZETA
    0x0397,	// GREEK CAPITAL LETTER ETA
    0x0398,	// GREEK CAPITAL LETTER THETA
    0x0399,	// GREEK CAPITAL LETTER IOTA
    0x039A,	// GREEK CAPITAL LETTER KAPPA
    0x039B,	// GREEK CAPITAL LETTER LAMDA
    0x039C,	// GREEK CAPITAL LETTER MU
    0x039D,	// GREEK CAPITAL LETTER NU
    0x039E,	// GREEK CAPITAL LETTER XI
    0x039F,	// GREEK CAPITAL LETTER OMICRON
    0x03A0,	// GREEK CAPITAL LETTER PI
    0x03A1,	// GREEK CAPITAL LETTER RHO
    0xfffd, // #UNDEFINED
    0x03A3,	// GREEK CAPITAL LETTER SIGMA
    0x03A4,	// GREEK CAPITAL LETTER TAU
    0x03A5,	// GREEK CAPITAL LETTER UPSILON
    0x03A6,	// GREEK CAPITAL LETTER PHI
    0x03A7,	// GREEK CAPITAL LETTER CHI
    0x03A8,	// GREEK CAPITAL LETTER PSI
    0x03A9,	// GREEK CAPITAL LETTER OMEGA
    0x03AA,	// GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
    0x03AB,	// GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
    0x03AC,	// GREEK SMALL LETTER ALPHA WITH TONOS
    0x03AD,	// GREEK SMALL LETTER EPSILON WITH TONOS
    0x03AE,	// GREEK SMALL LETTER ETA WITH TONOS
    0x03AF,	// GREEK SMALL LETTER IOTA WITH TONOS
    0x03B0,	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA AND TONOS
    0x03B1,	// GREEK SMALL LETTER ALPHA
    0x03B2,	// GREEK SMALL LETTER BETA
    0x03B3,	// GREEK SMALL LETTER GAMMA
    0x03B4,	// GREEK SMALL LETTER DELTA
    0x03B5,	// GREEK SMALL LETTER EPSILON
    0x03B6,	// GREEK SMALL LETTER ZETA
    0x03B7,	// GREEK SMALL LETTER ETA
    0x03B8,	// GREEK SMALL LETTER THETA
    0x03B9,	// GREEK SMALL LETTER IOTA
    0x03BA,	// GREEK SMALL LETTER KAPPA
    0x03BB,	// GREEK SMALL LETTER LAMDA
    0x03BC,	// GREEK SMALL LETTER MU
    0x03BD,	// GREEK SMALL LETTER NU
    0x03BE,	// GREEK SMALL LETTER XI
    0x03BF,	// GREEK SMALL LETTER OMICRON
    0x03C0,	// GREEK SMALL LETTER PI
    0x03C1,	// GREEK SMALL LETTER RHO
    0x03C2,	// GREEK SMALL LETTER FINAL SIGMA
    0x03C3,	// GREEK SMALL LETTER SIGMA
    0x03C4,	// GREEK SMALL LETTER TAU
    0x03C5,	// GREEK SMALL LETTER UPSILON
    0x03C6,	// GREEK SMALL LETTER PHI
    0x03C7,	// GREEK SMALL LETTER CHI
    0x03C8,	// GREEK SMALL LETTER PSI
    0x03C9,	// GREEK SMALL LETTER OMEGA
    0x03CA,	// GREEK SMALL LETTER IOTA WITH DIALYTIKA
    0x03CB,	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA
    0x03CC,	// GREEK SMALL LETTER OMICRON WITH TONOS
    0x03CD,	// GREEK SMALL LETTER UPSILON WITH TONOS
    0x03CE,	// GREEK SMALL LETTER OMEGA WITH TONOS
    0xfffd // #UNDEFINED
};

static const CharSetCvt::MapEnt toCP1253[] = {
{0x00A0, 0xA0},	// NO-BREAK SPACE
{0x00A3, 0xA3},	// POUND SIGN
{0x00A4, 0xA4},	// CURRENCY SIGN
{0x00A5, 0xA5},	// YEN SIGN
{0x00A6, 0xA6},	// BROKEN BAR
{0x00A7, 0xA7},	// SECTION SIGN
{0x00A8, 0xA8},	// DIAERESIS
{0x00A9, 0xA9},	// COPYRIGHT SIGN
{0x00AB, 0xAB},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xAC},	// NOT SIGN
{0x00AD, 0xAD},	// SOFT HYPHEN
{0x00AE, 0xAE},	// REGISTERED SIGN
{0x00B0, 0xB0},	// DEGREE SIGN
{0x00B1, 0xB1},	// PLUS-MINUS SIGN
{0x00B2, 0xB2},	// SUPERSCRIPT TWO
{0x00B3, 0xB3},	// SUPERSCRIPT THREE
{0x00B5, 0xB5},	// MICRO SIGN
{0x00B6, 0xB6},	// PILCROW SIGN
{0x00B7, 0xB7},	// MIDDLE DOT
{0x00BB, 0xBB},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00BD, 0xBD},	// VULGAR FRACTION ONE HALF
{0x0192, 0x83},	// LATIN SMALL LETTER F WITH HOOK
{0x0384, 0xB4},	// GREEK TONOS
{0x0385, 0xA1},	// GREEK DIALYTIKA TONOS
{0x0386, 0xA2},	// GREEK CAPITAL LETTER ALPHA WITH TONOS
{0x0388, 0xB8},	// GREEK CAPITAL LETTER EPSILON WITH TONOS
{0x0389, 0xB9},	// GREEK CAPITAL LETTER ETA WITH TONOS
{0x038A, 0xBA},	// GREEK CAPITAL LETTER IOTA WITH TONOS
{0x038C, 0xBC},	// GREEK CAPITAL LETTER OMICRON WITH TONOS
{0x038E, 0xBE},	// GREEK CAPITAL LETTER UPSILON WITH TONOS
{0x038F, 0xBF},	// GREEK CAPITAL LETTER OMEGA WITH TONOS
{0x0390, 0xC0},	// GREEK SMALL LETTER IOTA WITH DIALYTIKA AND TONOS
{0x0391, 0xC1},	// GREEK CAPITAL LETTER ALPHA
{0x0392, 0xC2},	// GREEK CAPITAL LETTER BETA
{0x0393, 0xC3},	// GREEK CAPITAL LETTER GAMMA
{0x0394, 0xC4},	// GREEK CAPITAL LETTER DELTA
{0x0395, 0xC5},	// GREEK CAPITAL LETTER EPSILON
{0x0396, 0xC6},	// GREEK CAPITAL LETTER ZETA
{0x0397, 0xC7},	// GREEK CAPITAL LETTER ETA
{0x0398, 0xC8},	// GREEK CAPITAL LETTER THETA
{0x0399, 0xC9},	// GREEK CAPITAL LETTER IOTA
{0x039A, 0xCA},	// GREEK CAPITAL LETTER KAPPA
{0x039B, 0xCB},	// GREEK CAPITAL LETTER LAMDA
{0x039C, 0xCC},	// GREEK CAPITAL LETTER MU
{0x039D, 0xCD},	// GREEK CAPITAL LETTER NU
{0x039E, 0xCE},	// GREEK CAPITAL LETTER XI
{0x039F, 0xCF},	// GREEK CAPITAL LETTER OMICRON
{0x03A0, 0xD0},	// GREEK CAPITAL LETTER PI
{0x03A1, 0xD1},	// GREEK CAPITAL LETTER RHO
{0x03A3, 0xD3},	// GREEK CAPITAL LETTER SIGMA
{0x03A4, 0xD4},	// GREEK CAPITAL LETTER TAU
{0x03A5, 0xD5},	// GREEK CAPITAL LETTER UPSILON
{0x03A6, 0xD6},	// GREEK CAPITAL LETTER PHI
{0x03A7, 0xD7},	// GREEK CAPITAL LETTER CHI
{0x03A8, 0xD8},	// GREEK CAPITAL LETTER PSI
{0x03A9, 0xD9},	// GREEK CAPITAL LETTER OMEGA
{0x03AA, 0xDA},	// GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
{0x03AB, 0xDB},	// GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
{0x03AC, 0xDC},	// GREEK SMALL LETTER ALPHA WITH TONOS
{0x03AD, 0xDD},	// GREEK SMALL LETTER EPSILON WITH TONOS
{0x03AE, 0xDE},	// GREEK SMALL LETTER ETA WITH TONOS
{0x03AF, 0xDF},	// GREEK SMALL LETTER IOTA WITH TONOS
{0x03B0, 0xE0},	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA AND TONOS
{0x03B1, 0xE1},	// GREEK SMALL LETTER ALPHA
{0x03B2, 0xE2},	// GREEK SMALL LETTER BETA
{0x03B3, 0xE3},	// GREEK SMALL LETTER GAMMA
{0x03B4, 0xE4},	// GREEK SMALL LETTER DELTA
{0x03B5, 0xE5},	// GREEK SMALL LETTER EPSILON
{0x03B6, 0xE6},	// GREEK SMALL LETTER ZETA
{0x03B7, 0xE7},	// GREEK SMALL LETTER ETA
{0x03B8, 0xE8},	// GREEK SMALL LETTER THETA
{0x03B9, 0xE9},	// GREEK SMALL LETTER IOTA
{0x03BA, 0xEA},	// GREEK SMALL LETTER KAPPA
{0x03BB, 0xEB},	// GREEK SMALL LETTER LAMDA
{0x03BC, 0xEC},	// GREEK SMALL LETTER MU
{0x03BD, 0xED},	// GREEK SMALL LETTER NU
{0x03BE, 0xEE},	// GREEK SMALL LETTER XI
{0x03BF, 0xEF},	// GREEK SMALL LETTER OMICRON
{0x03C0, 0xF0},	// GREEK SMALL LETTER PI
{0x03C1, 0xF1},	// GREEK SMALL LETTER RHO
{0x03C2, 0xF2},	// GREEK SMALL LETTER FINAL SIGMA
{0x03C3, 0xF3},	// GREEK SMALL LETTER SIGMA
{0x03C4, 0xF4},	// GREEK SMALL LETTER TAU
{0x03C5, 0xF5},	// GREEK SMALL LETTER UPSILON
{0x03C6, 0xF6},	// GREEK SMALL LETTER PHI
{0x03C7, 0xF7},	// GREEK SMALL LETTER CHI
{0x03C8, 0xF8},	// GREEK SMALL LETTER PSI
{0x03C9, 0xF9},	// GREEK SMALL LETTER OMEGA
{0x03CA, 0xFA},	// GREEK SMALL LETTER IOTA WITH DIALYTIKA
{0x03CB, 0xFB},	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA
{0x03CC, 0xFC},	// GREEK SMALL LETTER OMICRON WITH TONOS
{0x03CD, 0xFD},	// GREEK SMALL LETTER UPSILON WITH TONOS
{0x03CE, 0xFE},	// GREEK SMALL LETTER OMEGA WITH TONOS
{0x2013, 0x96},	// EN DASH
{0x2014, 0x97},	// EM DASH
{0x2015, 0xAF},	// HORIZONTAL BAR
{0x2018, 0x91},	// LEFT SINGLE QUOTATION MARK
{0x2019, 0x92},	// RIGHT SINGLE QUOTATION MARK
{0x201A, 0x82},	// SINGLE LOW-9 QUOTATION MARK
{0x201C, 0x93},	// LEFT DOUBLE QUOTATION MARK
{0x201D, 0x94},	// RIGHT DOUBLE QUOTATION MARK
{0x201E, 0x84},	// DOUBLE LOW-9 QUOTATION MARK
{0x2020, 0x86},	// DAGGER
{0x2021, 0x87},	// DOUBLE DAGGER
{0x2022, 0x95},	// BULLET
{0x2026, 0x85},	// HORIZONTAL ELLIPSIS
{0x2030, 0x89},	// PER MILLE SIGN
{0x2039, 0x8B},	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
{0x203A, 0x9B},	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
{0x20AC, 0x80},	// EURO SIGN
{0x2122, 0x99}	// TRADE MARK SIGN
};

const static unsigned short from8859_7[] = {
    0x00A0,	// 	NO-BREAK SPACE
    0x2018,	// 	LEFT SINGLE QUOTATION MARK
    0x2019,	// 	RIGHT SINGLE QUOTATION MARK
    0x00A3,	// 	POUND SIGN
    0x20AC,	// 	EURO SIGN
    0x20AF,	// 	DRACHMA SIGN
    0x00A6,	// 	BROKEN BAR
    0x00A7,	// 	SECTION SIGN
    0x00A8,	// 	DIAERESIS
    0x00A9,	// 	COPYRIGHT SIGN
    0x037A,	// 	GREEK YPOGEGRAMMENI
    0x00AB,	// 	LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x00AC,	// 	NOT SIGN
    0x00AD,	// 	SOFT HYPHEN
    0xfffd,	//	undefined
    0X2015,	// 	HORIZONTAL BAR
    0x00B0,	// 	DEGREE SIGN
    0x00B1,	// 	PLUS-MINUS SIGN
    0x00B2,	// 	SUPERSCRIPT TWO
    0x00B3,	// 	SUPERSCRIPT THREE
    0x0384,	// 	GREEK TONOS
    0x0385,	// 	GREEK DIALYTIKA TONOS
    0x0386,	// 	GREEK CAPITAL LETTER ALPHA WITH TONOS
    0x00B7,	// 	MIDDLE DOT
    0x0388,	// 	GREEK CAPITAL LETTER EPSILON WITH TONOS
    0x0389,	// 	GREEK CAPITAL LETTER ETA WITH TONOS
    0x038A,	// 	GREEK CAPITAL LETTER IOTA WITH TONOS
    0x00BB,	// 	RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    0x038C,	// 	GREEK CAPITAL LETTER OMICRON WITH TONOS
    0x00BD,	// 	VULGAR FRACTION ONE HALF
    0x038E,	// 	GREEK CAPITAL LETTER UPSILON WITH TONOS
    0x038F,	// 	GREEK CAPITAL LETTER OMEGA WITH TONOS
    0x0390,	// 	GREEK SMALL LETTER IOTA WITH DIALYTIKA AND TONOS
    0x0391,	// 	GREEK CAPITAL LETTER ALPHA
    0x0392,	// 	GREEK CAPITAL LETTER BETA
    0x0393,	// 	GREEK CAPITAL LETTER GAMMA
    0x0394,	// 	GREEK CAPITAL LETTER DELTA
    0x0395,	// 	GREEK CAPITAL LETTER EPSILON
    0x0396,	// 	GREEK CAPITAL LETTER ZETA
    0x0397,	// 	GREEK CAPITAL LETTER ETA
    0x0398,	// 	GREEK CAPITAL LETTER THETA
    0x0399,	// 	GREEK CAPITAL LETTER IOTA
    0x039A,	// 	GREEK CAPITAL LETTER KAPPA
    0x039B,	// 	GREEK CAPITAL LETTER LAMDA
    0x039C,	// 	GREEK CAPITAL LETTER MU
    0x039D,	// 	GREEK CAPITAL LETTER NU
    0x039E,	// 	GREEK CAPITAL LETTER XI
    0x039F,	// 	GREEK CAPITAL LETTER OMICRON
    0x03A0,	// 	GREEK CAPITAL LETTER PI
    0xfffd,	//	undefined
    0x03A1,	// 	GREEK CAPITAL LETTER RHO
    0x03A3,	// 	GREEK CAPITAL LETTER SIGMA
    0x03A4,	// 	GREEK CAPITAL LETTER TAU
    0x03A5,	// 	GREEK CAPITAL LETTER UPSILON
    0x03A6,	// 	GREEK CAPITAL LETTER PHI
    0x03A7,	// 	GREEK CAPITAL LETTER CHI
    0x03A8,	// 	GREEK CAPITAL LETTER PSI
    0x03A9,	// 	GREEK CAPITAL LETTER OMEGA
    0x03AA,	// 	GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
    0x03AB,	// 	GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
    0x03AC,	// 	GREEK SMALL LETTER ALPHA WITH TONOS
    0x03AD,	// 	GREEK SMALL LETTER EPSILON WITH TONOS
    0x03AE,	// 	GREEK SMALL LETTER ETA WITH TONOS
    0x03AF,	// 	GREEK SMALL LETTER IOTA WITH TONOS
    0x03B0,	// 	GREEK SMALL LETTER UPSILON WITH DIALYTIKA AND TONOS
    0x03B1,	// 	GREEK SMALL LETTER ALPHA
    0x03B2,	// 	GREEK SMALL LETTER BETA
    0x03B3,	// 	GREEK SMALL LETTER GAMMA
    0x03B4,	// 	GREEK SMALL LETTER DELTA
    0x03B5,	// 	GREEK SMALL LETTER EPSILON
    0x03B6,	// 	GREEK SMALL LETTER ZETA
    0x03B7,	// 	GREEK SMALL LETTER ETA
    0x03B8,	// 	GREEK SMALL LETTER THETA
    0x03B9,	// 	GREEK SMALL LETTER IOTA
    0x03BA,	// 	GREEK SMALL LETTER KAPPA
    0x03BB,	// 	GREEK SMALL LETTER LAMDA
    0x03BC,	// 	GREEK SMALL LETTER MU
    0x03BD,	// 	GREEK SMALL LETTER NU
    0x03BE,	// 	GREEK SMALL LETTER XI
    0x03BF,	// 	GREEK SMALL LETTER OMICRON
    0x03C0,	// 	GREEK SMALL LETTER PI
    0x03C1,	// 	GREEK SMALL LETTER RHO
    0x03C2,	// 	GREEK SMALL LETTER FINAL SIGMA
    0x03C3,	// 	GREEK SMALL LETTER SIGMA
    0x03C4,	// 	GREEK SMALL LETTER TAU
    0x03C5,	// 	GREEK SMALL LETTER UPSILON
    0x03C6,	// 	GREEK SMALL LETTER PHI
    0x03C7,	// 	GREEK SMALL LETTER CHI
    0x03C8,	// 	GREEK SMALL LETTER PSI
    0x03C9,	// 	GREEK SMALL LETTER OMEGA
    0x03CA,	// 	GREEK SMALL LETTER IOTA WITH DIALYTIKA
    0x03CB,	//	GREEK SMALL LETTER UPSILON WITH DIALYTIKA
    0x03CC,	// 	GREEK SMALL LETTER OMICRON WITH TONOS
    0x03CD,	// 	GREEK SMALL LETTER UPSILON WITH TONOS
    0x03CE,	// 	GREEK SMALL LETTER OMEGA WITH TONOS
    0xfffd	//	undefined
};

static const CharSetCvt::MapEnt to8859_7[] = {
{0x00A0, 0xA0},	// 	NO-BREAK SPACE
{0x00A3, 0xA3},	// 	POUND SIGN
{0x00A6, 0xA6},	// 	BROKEN BAR
{0x00A7, 0xA7},	// 	SECTION SIGN
{0x00A8, 0xA8},	// 	DIAERESIS
{0x00A9, 0xA9},	// 	COPYRIGHT SIGN
{0x00AB, 0xAB},	// 	LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xAC},	// 	NOT SIGN
{0x00AD, 0xAD},	// 	SOFT HYPHEN
{0x00B0, 0xB0},	// 	DEGREE SIGN
{0x00B1, 0xB1},	// 	PLUS-MINUS SIGN
{0x00B2, 0xB2},	// 	SUPERSCRIPT TWO
{0x00B3, 0xB3},	// 	SUPERSCRIPT THREE
{0x00B7, 0xB7},	// 	MIDDLE DOT
{0x00BB, 0xBB},	// 	RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00BD, 0xBD},	// 	VULGAR FRACTION ONE HALF
{0x037A, 0xAA},	// 	GREEK YPOGEGRAMMENI
{0x0384, 0xB4},	// 	GREEK TONOS
{0x0385, 0xB5},	// 	GREEK DIALYTIKA TONOS
{0x0386, 0xB6},	// 	GREEK CAPITAL LETTER ALPHA WITH TONOS
{0x0388, 0xB8},	// 	GREEK CAPITAL LETTER EPSILON WITH TONOS
{0x0389, 0xB9},	// 	GREEK CAPITAL LETTER ETA WITH TONOS
{0x038A, 0xBA},	// 	GREEK CAPITAL LETTER IOTA WITH TONOS
{0x038C, 0xBC},	// 	GREEK CAPITAL LETTER OMICRON WITH TONOS
{0x038E, 0xBE},	// 	GREEK CAPITAL LETTER UPSILON WITH TONOS
{0x038F, 0xBF},	// 	GREEK CAPITAL LETTER OMEGA WITH TONOS
{0x0390, 0xC0},	// 	GREEK SMALL LETTER IOTA WITH DIALYTIKA AND TONOS
{0x0391, 0xC1},	// 	GREEK CAPITAL LETTER ALPHA
{0x0392, 0xC2},	// 	GREEK CAPITAL LETTER BETA
{0x0393, 0xC3},	// 	GREEK CAPITAL LETTER GAMMA
{0x0394, 0xC4},	// 	GREEK CAPITAL LETTER DELTA
{0x0395, 0xC5},	// 	GREEK CAPITAL LETTER EPSILON
{0x0396, 0xC6},	// 	GREEK CAPITAL LETTER ZETA
{0x0397, 0xC7},	// 	GREEK CAPITAL LETTER ETA
{0x0398, 0xC8},	// 	GREEK CAPITAL LETTER THETA
{0x0399, 0xC9},	// 	GREEK CAPITAL LETTER IOTA
{0x039A, 0xCA},	// 	GREEK CAPITAL LETTER KAPPA
{0x039B, 0xCB},	// 	GREEK CAPITAL LETTER LAMDA
{0x039C, 0xCC},	// 	GREEK CAPITAL LETTER MU
{0x039D, 0xCD},	// 	GREEK CAPITAL LETTER NU
{0x039E, 0xCE},	// 	GREEK CAPITAL LETTER XI
{0x039F, 0xCF},	// 	GREEK CAPITAL LETTER OMICRON
{0x03A0, 0xD0},	// 	GREEK CAPITAL LETTER PI
{0x03A1, 0xD1},	// 	GREEK CAPITAL LETTER RHO
{0x03A3, 0xD3},	// 	GREEK CAPITAL LETTER SIGMA
{0x03A4, 0xD4},	// 	GREEK CAPITAL LETTER TAU
{0x03A5, 0xD5},	// 	GREEK CAPITAL LETTER UPSILON
{0x03A6, 0xD6},	// 	GREEK CAPITAL LETTER PHI
{0x03A7, 0xD7},	// 	GREEK CAPITAL LETTER CHI
{0x03A8, 0xD8},	// 	GREEK CAPITAL LETTER PSI
{0x03A9, 0xD9},	// 	GREEK CAPITAL LETTER OMEGA
{0x03AA, 0xDA},	// 	GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
{0x03AB, 0xDB},	// 	GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
{0x03AC, 0xDC},	// 	GREEK SMALL LETTER ALPHA WITH TONOS
{0x03AD, 0xDD},	// 	GREEK SMALL LETTER EPSILON WITH TONOS
{0x03AE, 0xDE},	// 	GREEK SMALL LETTER ETA WITH TONOS
{0x03AF, 0xDF},	// 	GREEK SMALL LETTER IOTA WITH TONOS
{0x03B0, 0xE0},	// 	GREEK SMALL LETTER UPSILON WITH DIALYTIKA AND TONOS
{0x03B1, 0xE1},	// 	GREEK SMALL LETTER ALPHA
{0x03B2, 0xE2},	// 	GREEK SMALL LETTER BETA
{0x03B3, 0xE3},	// 	GREEK SMALL LETTER GAMMA
{0x03B4, 0xE4},	// 	GREEK SMALL LETTER DELTA
{0x03B5, 0xE5},	// 	GREEK SMALL LETTER EPSILON
{0x03B6, 0xE6},	// 	GREEK SMALL LETTER ZETA
{0x03B7, 0xE7},	// 	GREEK SMALL LETTER ETA
{0x03B8, 0xE8},	// 	GREEK SMALL LETTER THETA
{0x03B9, 0xE9},	// 	GREEK SMALL LETTER IOTA
{0x03BA, 0xEA},	// 	GREEK SMALL LETTER KAPPA
{0x03BB, 0xEB},	// 	GREEK SMALL LETTER LAMDA
{0x03BC, 0xEC},	// 	GREEK SMALL LETTER MU
{0x03BD, 0xED},	// 	GREEK SMALL LETTER NU
{0x03BE, 0xEE},	// 	GREEK SMALL LETTER XI
{0x03BF, 0xEF},	// 	GREEK SMALL LETTER OMICRON
{0x03C0, 0xF0},	// 	GREEK SMALL LETTER PI
{0x03C1, 0xF1},	// 	GREEK SMALL LETTER RHO
{0x03C2, 0xF2},	// 	GREEK SMALL LETTER FINAL SIGMA
{0x03C3, 0xF3},	// 	GREEK SMALL LETTER SIGMA
{0x03C4, 0xF4},	// 	GREEK SMALL LETTER TAU
{0x03C5, 0xF5},	// 	GREEK SMALL LETTER UPSILON
{0x03C6, 0xF6},	// 	GREEK SMALL LETTER PHI
{0x03C7, 0xF7},	// 	GREEK SMALL LETTER CHI
{0x03C8, 0xF8},	// 	GREEK SMALL LETTER PSI
{0x03C9, 0xF9},	// 	GREEK SMALL LETTER OMEGA
{0x03CA, 0xFA},	// 	GREEK SMALL LETTER IOTA WITH DIALYTIKA
{0x03CB, 0xFB},	// 	GREEK SMALL LETTER UPSILON WITH DIALYTIKA
{0x03CC, 0xFC},	// 	GREEK SMALL LETTER OMICRON WITH TONOS
{0x03CD, 0xFD},	// 	GREEK SMALL LETTER UPSILON WITH TONOS
{0x03CE, 0xFE},	// 	GREEK SMALL LETTER OMEGA WITH TONOS
{0x2015, 0xAF},	// 	HORIZONTAL BAR
{0x2018, 0xA1},	// 	LEFT SINGLE QUOTATION MARK
{0x2019, 0xA2},	// 	RIGHT SINGLE QUOTATION MARK
{0x20AC, 0xA4},	// 	EURO SIGN
{0x20AF, 0xA5}	// 	DRACHMA SIGN
};

const static unsigned short from737[] = {
    0x0391,	// GREEK CAPITAL LETTER ALPHA
    0x0392,	// GREEK CAPITAL LETTER BETA
    0x0393,	// GREEK CAPITAL LETTER GAMMA
    0x0394,	// GREEK CAPITAL LETTER DELTA
    0x0395,	// GREEK CAPITAL LETTER EPSILON
    0x0396,	// GREEK CAPITAL LETTER ZETA
    0x0397,	// GREEK CAPITAL LETTER ETA
    0x0398,	// GREEK CAPITAL LETTER THETA
    0x0399,	// GREEK CAPITAL LETTER IOTA
    0x039a,	// GREEK CAPITAL LETTER KAPPA
    0x039b,	// GREEK CAPITAL LETTER LAMDA
    0x039c,	// GREEK CAPITAL LETTER MU
    0x039d,	// GREEK CAPITAL LETTER NU
    0x039e,	// GREEK CAPITAL LETTER XI
    0x039f,	// GREEK CAPITAL LETTER OMICRON
    0x03a0,	// GREEK CAPITAL LETTER PI
    0x03a1,	// GREEK CAPITAL LETTER RHO
    0x03a3,	// GREEK CAPITAL LETTER SIGMA
    0x03a4,	// GREEK CAPITAL LETTER TAU
    0x03a5,	// GREEK CAPITAL LETTER UPSILON
    0x03a6,	// GREEK CAPITAL LETTER PHI
    0x03a7,	// GREEK CAPITAL LETTER CHI
    0x03a8,	// GREEK CAPITAL LETTER PSI
    0x03a9,	// GREEK CAPITAL LETTER OMEGA
    0x03b1,	// GREEK SMALL LETTER ALPHA
    0x03b2,	// GREEK SMALL LETTER BETA
    0x03b3,	// GREEK SMALL LETTER GAMMA
    0x03b4,	// GREEK SMALL LETTER DELTA
    0x03b5,	// GREEK SMALL LETTER EPSILON
    0x03b6,	// GREEK SMALL LETTER ZETA
    0x03b7,	// GREEK SMALL LETTER ETA
    0x03b8,	// GREEK SMALL LETTER THETA
    0x03b9,	// GREEK SMALL LETTER IOTA
    0x03ba,	// GREEK SMALL LETTER KAPPA
    0x03bb,	// GREEK SMALL LETTER LAMDA
    0x03bc,	// GREEK SMALL LETTER MU
    0x03bd,	// GREEK SMALL LETTER NU
    0x03be,	// GREEK SMALL LETTER XI
    0x03bf,	// GREEK SMALL LETTER OMICRON
    0x03c0,	// GREEK SMALL LETTER PI
    0x03c1,	// GREEK SMALL LETTER RHO
    0x03c3,	// GREEK SMALL LETTER SIGMA
    0x03c2,	// GREEK SMALL LETTER FINAL SIGMA
    0x03c4,	// GREEK SMALL LETTER TAU
    0x03c5,	// GREEK SMALL LETTER UPSILON
    0x03c6,	// GREEK SMALL LETTER PHI
    0x03c7,	// GREEK SMALL LETTER CHI
    0x03c8,	// GREEK SMALL LETTER PSI
    0x2591,	// LIGHT SHADE
    0x2592,	// MEDIUM SHADE
    0x2593,	// DARK SHADE
    0x2502,	// BOX DRAWINGS LIGHT VERTICAL
    0x2524,	// BOX DRAWINGS LIGHT VERTICAL AND LEFT
    0x2561,	// BOX DRAWINGS VERTICAL SINGLE AND LEFT DOUBLE
    0x2562,	// BOX DRAWINGS VERTICAL DOUBLE AND LEFT SINGLE
    0x2556,	// BOX DRAWINGS DOWN DOUBLE AND LEFT SINGLE
    0x2555,	// BOX DRAWINGS DOWN SINGLE AND LEFT DOUBLE
    0x2563,	// BOX DRAWINGS DOUBLE VERTICAL AND LEFT
    0x2551,	// BOX DRAWINGS DOUBLE VERTICAL
    0x2557,	// BOX DRAWINGS DOUBLE DOWN AND LEFT
    0x255d,	// BOX DRAWINGS DOUBLE UP AND LEFT
    0x255c,	// BOX DRAWINGS UP DOUBLE AND LEFT SINGLE
    0x255b,	// BOX DRAWINGS UP SINGLE AND LEFT DOUBLE
    0x2510,	// BOX DRAWINGS LIGHT DOWN AND LEFT
    0x2514,	// BOX DRAWINGS LIGHT UP AND RIGHT
    0x2534,	// BOX DRAWINGS LIGHT UP AND HORIZONTAL
    0x252c,	// BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
    0x251c,	// BOX DRAWINGS LIGHT VERTICAL AND RIGHT
    0x2500,	// BOX DRAWINGS LIGHT HORIZONTAL
    0x253c,	// BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
    0x255e,	// BOX DRAWINGS VERTICAL SINGLE AND RIGHT DOUBLE
    0x255f,	// BOX DRAWINGS VERTICAL DOUBLE AND RIGHT SINGLE
    0x255a,	// BOX DRAWINGS DOUBLE UP AND RIGHT
    0x2554,	// BOX DRAWINGS DOUBLE DOWN AND RIGHT
    0x2569,	// BOX DRAWINGS DOUBLE UP AND HORIZONTAL
    0x2566,	// BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
    0x2560,	// BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
    0x2550,	// BOX DRAWINGS DOUBLE HORIZONTAL
    0x256c,	// BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
    0x2567,	// BOX DRAWINGS UP SINGLE AND HORIZONTAL DOUBLE
    0x2568,	// BOX DRAWINGS UP DOUBLE AND HORIZONTAL SINGLE
    0x2564,	// BOX DRAWINGS DOWN SINGLE AND HORIZONTAL DOUBLE
    0x2565,	// BOX DRAWINGS DOWN DOUBLE AND HORIZONTAL SINGLE
    0x2559,	// BOX DRAWINGS UP DOUBLE AND RIGHT SINGLE
    0x2558,	// BOX DRAWINGS UP SINGLE AND RIGHT DOUBLE
    0x2552,	// BOX DRAWINGS DOWN SINGLE AND RIGHT DOUBLE
    0x2553,	// BOX DRAWINGS DOWN DOUBLE AND RIGHT SINGLE
    0x256b,	// BOX DRAWINGS VERTICAL DOUBLE AND HORIZONTAL SINGLE
    0x256a,	// BOX DRAWINGS VERTICAL SINGLE AND HORIZONTAL DOUBLE
    0x2518,	// BOX DRAWINGS LIGHT UP AND LEFT
    0x250c,	// BOX DRAWINGS LIGHT DOWN AND RIGHT
    0x2588,	// FULL BLOCK
    0x2584,	// LOWER HALF BLOCK
    0x258c,	// LEFT HALF BLOCK
    0x2590,	// RIGHT HALF BLOCK
    0x2580,	// UPPER HALF BLOCK
    0x03c9,	// GREEK SMALL LETTER OMEGA
    0x03ac,	// GREEK SMALL LETTER ALPHA WITH TONOS
    0x03ad,	// GREEK SMALL LETTER EPSILON WITH TONOS
    0x03ae,	// GREEK SMALL LETTER ETA WITH TONOS
    0x03ca,	// GREEK SMALL LETTER IOTA WITH DIALYTIKA
    0x03af,	// GREEK SMALL LETTER IOTA WITH TONOS
    0x03cc,	// GREEK SMALL LETTER OMICRON WITH TONOS
    0x03cd,	// GREEK SMALL LETTER UPSILON WITH TONOS
    0x03cb,	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA
    0x03ce,	// GREEK SMALL LETTER OMEGA WITH TONOS
    0x0386,	// GREEK CAPITAL LETTER ALPHA WITH TONOS
    0x0388,	// GREEK CAPITAL LETTER EPSILON WITH TONOS
    0x0389,	// GREEK CAPITAL LETTER ETA WITH TONOS
    0x038a,	// GREEK CAPITAL LETTER IOTA WITH TONOS
    0x038c,	// GREEK CAPITAL LETTER OMICRON WITH TONOS
    0x038e,	// GREEK CAPITAL LETTER UPSILON WITH TONOS
    0x038f,	// GREEK CAPITAL LETTER OMEGA WITH TONOS
    0x00b1,	// PLUS-MINUS SIGN
    0x2265,	// GREATER-THAN OR EQUAL TO
    0x2264,	// LESS-THAN OR EQUAL TO
    0x03aa,	// GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
    0x03ab,	// GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
    0x00f7,	// DIVISION SIGN
    0x2248,	// ALMOST EQUAL TO
    0x00b0,	// DEGREE SIGN
    0x2219,	// BULLET OPERATOR
    0x00b7,	// MIDDLE DOT
    0x221a,	// SQUARE ROOT
    0x207f,	// SUPERSCRIPT LATIN SMALL LETTER N
    0x00b2,	// SUPERSCRIPT TWO
    0x25a0,	// BLACK SQUARE
    0x00a0	// NO-BREAK SPACE
};

static const CharSetCvt::MapEnt to737[] = {
{0x00a0, 0xff},	// NO-BREAK SPACE
{0x00b0, 0xf8},	// DEGREE SIGN
{0x00b1, 0xf1},	// PLUS-MINUS SIGN
{0x00b2, 0xfd},	// SUPERSCRIPT TWO
{0x00b7, 0xfa},	// MIDDLE DOT
{0x00f7, 0xf6},	// DIVISION SIGN
{0x0386, 0xea},	// GREEK CAPITAL LETTER ALPHA WITH TONOS
{0x0388, 0xeb},	// GREEK CAPITAL LETTER EPSILON WITH TONOS
{0x0389, 0xec},	// GREEK CAPITAL LETTER ETA WITH TONOS
{0x038a, 0xed},	// GREEK CAPITAL LETTER IOTA WITH TONOS
{0x038c, 0xee},	// GREEK CAPITAL LETTER OMICRON WITH TONOS
{0x038e, 0xef},	// GREEK CAPITAL LETTER UPSILON WITH TONOS
{0x038f, 0xf0},	// GREEK CAPITAL LETTER OMEGA WITH TONOS
{0x0391, 0x80},	// GREEK CAPITAL LETTER ALPHA
{0x0392, 0x81},	// GREEK CAPITAL LETTER BETA
{0x0393, 0x82},	// GREEK CAPITAL LETTER GAMMA
{0x0394, 0x83},	// GREEK CAPITAL LETTER DELTA
{0x0395, 0x84},	// GREEK CAPITAL LETTER EPSILON
{0x0396, 0x85},	// GREEK CAPITAL LETTER ZETA
{0x0397, 0x86},	// GREEK CAPITAL LETTER ETA
{0x0398, 0x87},	// GREEK CAPITAL LETTER THETA
{0x0399, 0x88},	// GREEK CAPITAL LETTER IOTA
{0x039a, 0x89},	// GREEK CAPITAL LETTER KAPPA
{0x039b, 0x8a},	// GREEK CAPITAL LETTER LAMDA
{0x039c, 0x8b},	// GREEK CAPITAL LETTER MU
{0x039d, 0x8c},	// GREEK CAPITAL LETTER NU
{0x039e, 0x8d},	// GREEK CAPITAL LETTER XI
{0x039f, 0x8e},	// GREEK CAPITAL LETTER OMICRON
{0x03a0, 0x8f},	// GREEK CAPITAL LETTER PI
{0x03a1, 0x90},	// GREEK CAPITAL LETTER RHO
{0x03a3, 0x91},	// GREEK CAPITAL LETTER SIGMA
{0x03a4, 0x92},	// GREEK CAPITAL LETTER TAU
{0x03a5, 0x93},	// GREEK CAPITAL LETTER UPSILON
{0x03a6, 0x94},	// GREEK CAPITAL LETTER PHI
{0x03a7, 0x95},	// GREEK CAPITAL LETTER CHI
{0x03a8, 0x96},	// GREEK CAPITAL LETTER PSI
{0x03a9, 0x97},	// GREEK CAPITAL LETTER OMEGA
{0x03aa, 0xf4},	// GREEK CAPITAL LETTER IOTA WITH DIALYTIKA
{0x03ab, 0xf5},	// GREEK CAPITAL LETTER UPSILON WITH DIALYTIKA
{0x03ac, 0xe1},	// GREEK SMALL LETTER ALPHA WITH TONOS
{0x03ad, 0xe2},	// GREEK SMALL LETTER EPSILON WITH TONOS
{0x03ae, 0xe3},	// GREEK SMALL LETTER ETA WITH TONOS
{0x03af, 0xe5},	// GREEK SMALL LETTER IOTA WITH TONOS
{0x03b1, 0x98},	// GREEK SMALL LETTER ALPHA
{0x03b2, 0x99},	// GREEK SMALL LETTER BETA
{0x03b3, 0x9a},	// GREEK SMALL LETTER GAMMA
{0x03b4, 0x9b},	// GREEK SMALL LETTER DELTA
{0x03b5, 0x9c},	// GREEK SMALL LETTER EPSILON
{0x03b6, 0x9d},	// GREEK SMALL LETTER ZETA
{0x03b7, 0x9e},	// GREEK SMALL LETTER ETA
{0x03b8, 0x9f},	// GREEK SMALL LETTER THETA
{0x03b9, 0xa0},	// GREEK SMALL LETTER IOTA
{0x03ba, 0xa1},	// GREEK SMALL LETTER KAPPA
{0x03bb, 0xa2},	// GREEK SMALL LETTER LAMDA
{0x03bc, 0xa3},	// GREEK SMALL LETTER MU
{0x03bd, 0xa4},	// GREEK SMALL LETTER NU
{0x03be, 0xa5},	// GREEK SMALL LETTER XI
{0x03bf, 0xa6},	// GREEK SMALL LETTER OMICRON
{0x03c0, 0xa7},	// GREEK SMALL LETTER PI
{0x03c1, 0xa8},	// GREEK SMALL LETTER RHO
{0x03c2, 0xaa},	// GREEK SMALL LETTER FINAL SIGMA
{0x03c3, 0xa9},	// GREEK SMALL LETTER SIGMA
{0x03c4, 0xab},	// GREEK SMALL LETTER TAU
{0x03c5, 0xac},	// GREEK SMALL LETTER UPSILON
{0x03c6, 0xad},	// GREEK SMALL LETTER PHI
{0x03c7, 0xae},	// GREEK SMALL LETTER CHI
{0x03c8, 0xaf},	// GREEK SMALL LETTER PSI
{0x03c9, 0xe0},	// GREEK SMALL LETTER OMEGA
{0x03ca, 0xe4},	// GREEK SMALL LETTER IOTA WITH DIALYTIKA
{0x03cb, 0xe8},	// GREEK SMALL LETTER UPSILON WITH DIALYTIKA
{0x03cc, 0xe6},	// GREEK SMALL LETTER OMICRON WITH TONOS
{0x03cd, 0xe7},	// GREEK SMALL LETTER UPSILON WITH TONOS
{0x03ce, 0xe9},	// GREEK SMALL LETTER OMEGA WITH TONOS
{0x207f, 0xfc},	// SUPERSCRIPT LATIN SMALL LETTER N
{0x2219, 0xf9},	// BULLET OPERATOR
{0x221a, 0xfb},	// SQUARE ROOT
{0x2248, 0xf7},	// ALMOST EQUAL TO
{0x2264, 0xf3},	// LESS-THAN OR EQUAL TO
{0x2265, 0xf2},	// GREATER-THAN OR EQUAL TO
{0x2500, 0xc4},	// BOX DRAWINGS LIGHT HORIZONTAL
{0x2502, 0xb3},	// BOX DRAWINGS LIGHT VERTICAL
{0x250c, 0xda},	// BOX DRAWINGS LIGHT DOWN AND RIGHT
{0x2510, 0xbf},	// BOX DRAWINGS LIGHT DOWN AND LEFT
{0x2514, 0xc0},	// BOX DRAWINGS LIGHT UP AND RIGHT
{0x2518, 0xd9},	// BOX DRAWINGS LIGHT UP AND LEFT
{0x251c, 0xc3},	// BOX DRAWINGS LIGHT VERTICAL AND RIGHT
{0x2524, 0xb4},	// BOX DRAWINGS LIGHT VERTICAL AND LEFT
{0x252c, 0xc2},	// BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
{0x2534, 0xc1},	// BOX DRAWINGS LIGHT UP AND HORIZONTAL
{0x253c, 0xc5},	// BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
{0x2550, 0xcd},	// BOX DRAWINGS DOUBLE HORIZONTAL
{0x2551, 0xba},	// BOX DRAWINGS DOUBLE VERTICAL
{0x2552, 0xd5},	// BOX DRAWINGS DOWN SINGLE AND RIGHT DOUBLE
{0x2553, 0xd6},	// BOX DRAWINGS DOWN DOUBLE AND RIGHT SINGLE
{0x2554, 0xc9},	// BOX DRAWINGS DOUBLE DOWN AND RIGHT
{0x2555, 0xb8},	// BOX DRAWINGS DOWN SINGLE AND LEFT DOUBLE
{0x2556, 0xb7},	// BOX DRAWINGS DOWN DOUBLE AND LEFT SINGLE
{0x2557, 0xbb},	// BOX DRAWINGS DOUBLE DOWN AND LEFT
{0x2558, 0xd4},	// BOX DRAWINGS UP SINGLE AND RIGHT DOUBLE
{0x2559, 0xd3},	// BOX DRAWINGS UP DOUBLE AND RIGHT SINGLE
{0x255a, 0xc8},	// BOX DRAWINGS DOUBLE UP AND RIGHT
{0x255b, 0xbe},	// BOX DRAWINGS UP SINGLE AND LEFT DOUBLE
{0x255c, 0xbd},	// BOX DRAWINGS UP DOUBLE AND LEFT SINGLE
{0x255d, 0xbc},	// BOX DRAWINGS DOUBLE UP AND LEFT
{0x255e, 0xc6},	// BOX DRAWINGS VERTICAL SINGLE AND RIGHT DOUBLE
{0x255f, 0xc7},	// BOX DRAWINGS VERTICAL DOUBLE AND RIGHT SINGLE
{0x2560, 0xcc},	// BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
{0x2561, 0xb5},	// BOX DRAWINGS VERTICAL SINGLE AND LEFT DOUBLE
{0x2562, 0xb6},	// BOX DRAWINGS VERTICAL DOUBLE AND LEFT SINGLE
{0x2563, 0xb9},	// BOX DRAWINGS DOUBLE VERTICAL AND LEFT
{0x2564, 0xd1},	// BOX DRAWINGS DOWN SINGLE AND HORIZONTAL DOUBLE
{0x2565, 0xd2},	// BOX DRAWINGS DOWN DOUBLE AND HORIZONTAL SINGLE
{0x2566, 0xcb},	// BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
{0x2567, 0xcf},	// BOX DRAWINGS UP SINGLE AND HORIZONTAL DOUBLE
{0x2568, 0xd0},	// BOX DRAWINGS UP DOUBLE AND HORIZONTAL SINGLE
{0x2569, 0xca},	// BOX DRAWINGS DOUBLE UP AND HORIZONTAL
{0x256a, 0xd8},	// BOX DRAWINGS VERTICAL SINGLE AND HORIZONTAL DOUBLE
{0x256b, 0xd7},	// BOX DRAWINGS VERTICAL DOUBLE AND HORIZONTAL SINGLE
{0x256c, 0xce},	// BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
{0x2580, 0xdf},	// UPPER HALF BLOCK
{0x2584, 0xdc},	// LOWER HALF BLOCK
{0x2588, 0xdb},	// FULL BLOCK
{0x258c, 0xdd},	// LEFT HALF BLOCK
{0x2590, 0xde},	// RIGHT HALF BLOCK
{0x2591, 0xb0},	// LIGHT SHADE
{0x2592, 0xb1},	// MEDIUM SHADE
{0x2593, 0xb2},	// DARK SHADE
{0x25a0, 0xfe}	// BLACK SQUARE
};

const static unsigned short from8859_2[] = {
	0x00A0,	//	NO-BREAK SPACE
	0x0104,	//	LATIN CAPITAL LETTER A WITH OGONEK
	0x02D8,	//	BREVE
	0x0141,	//	LATIN CAPITAL LETTER L WITH STROKE
	0x00A4,	//	CURRENCY SIGN
	0x013D,	//	LATIN CAPITAL LETTER L WITH CARON
	0x015A,	//	LATIN CAPITAL LETTER S WITH ACUTE
	0x00A7,	//	SECTION SIGN
	0x00A8,	//	DIAERESIS
	0x0160,	//	LATIN CAPITAL LETTER S WITH CARON
	0x015E,	//	LATIN CAPITAL LETTER S WITH CEDILLA
	0x0164,	//	LATIN CAPITAL LETTER T WITH CARON
	0x0179,	//	LATIN CAPITAL LETTER Z WITH ACUTE
	0x00AD,	//	SOFT HYPHEN
	0x017D,	//	LATIN CAPITAL LETTER Z WITH CARON
	0x017B,	//	LATIN CAPITAL LETTER Z WITH DOT ABOVE
	0x00B0,	//	DEGREE SIGN
	0x0105,	//	LATIN SMALL LETTER A WITH OGONEK
	0x02DB,	//	OGONEK
	0x0142,	//	LATIN SMALL LETTER L WITH STROKE
	0x00B4,	//	ACUTE ACCENT
	0x013E,	//	LATIN SMALL LETTER L WITH CARON
	0x015B,	//	LATIN SMALL LETTER S WITH ACUTE
	0x02C7,	//	CARON
	0x00B8,	//	CEDILLA
	0x0161,	//	LATIN SMALL LETTER S WITH CARON
	0x015F,	//	LATIN SMALL LETTER S WITH CEDILLA
	0x0165,	//	LATIN SMALL LETTER T WITH CARON
	0x017A,	//	LATIN SMALL LETTER Z WITH ACUTE
	0x02DD,	//	DOUBLE ACUTE ACCENT
	0x017E,	//	LATIN SMALL LETTER Z WITH CARON
	0x017C,	//	LATIN SMALL LETTER Z WITH DOT ABOVE
	0x0154,	//	LATIN CAPITAL LETTER R WITH ACUTE
	0x00C1,	//	LATIN CAPITAL LETTER A WITH ACUTE
	0x00C2,	//	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
	0x0102,	//	LATIN CAPITAL LETTER A WITH BREVE
	0x00C4,	//	LATIN CAPITAL LETTER A WITH DIAERESIS
	0x0139,	//	LATIN CAPITAL LETTER L WITH ACUTE
	0x0106,	//	LATIN CAPITAL LETTER C WITH ACUTE
	0x00C7,	//	LATIN CAPITAL LETTER C WITH CEDILLA
	0x010C,	//	LATIN CAPITAL LETTER C WITH CARON
	0x00C9,	//	LATIN CAPITAL LETTER E WITH ACUTE
	0x0118,	//	LATIN CAPITAL LETTER E WITH OGONEK
	0x00CB,	//	LATIN CAPITAL LETTER E WITH DIAERESIS
	0x011A,	//	LATIN CAPITAL LETTER E WITH CARON
	0x00CD,	//	LATIN CAPITAL LETTER I WITH ACUTE
	0x00CE,	//	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
	0x010E,	//	LATIN CAPITAL LETTER D WITH CARON
	0x0110,	//	LATIN CAPITAL LETTER D WITH STROKE
	0x0143,	//	LATIN CAPITAL LETTER N WITH ACUTE
	0x0147,	//	LATIN CAPITAL LETTER N WITH CARON
	0x00D3,	//	LATIN CAPITAL LETTER O WITH ACUTE
	0x00D4,	//	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
	0x0150,	//	LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
	0x00D6,	//	LATIN CAPITAL LETTER O WITH DIAERESIS
	0x00D7,	//	MULTIPLICATION SIGN
	0x0158,	//	LATIN CAPITAL LETTER R WITH CARON
	0x016E,	//	LATIN CAPITAL LETTER U WITH RING ABOVE
	0x00DA,	//	LATIN CAPITAL LETTER U WITH ACUTE
	0x0170,	//	LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
	0x00DC,	//	LATIN CAPITAL LETTER U WITH DIAERESIS
	0x00DD,	//	LATIN CAPITAL LETTER Y WITH ACUTE
	0x0162,	//	LATIN CAPITAL LETTER T WITH CEDILLA
	0x00DF,	//	LATIN SMALL LETTER SHARP S
	0x0155,	//	LATIN SMALL LETTER R WITH ACUTE
	0x00E1,	//	LATIN SMALL LETTER A WITH ACUTE
	0x00E2,	//	LATIN SMALL LETTER A WITH CIRCUMFLEX
	0x0103,	//	LATIN SMALL LETTER A WITH BREVE
	0x00E4,	//	LATIN SMALL LETTER A WITH DIAERESIS
	0x013A,	//	LATIN SMALL LETTER L WITH ACUTE
	0x0107,	//	LATIN SMALL LETTER C WITH ACUTE
	0x00E7,	//	LATIN SMALL LETTER C WITH CEDILLA
	0x010D,	//	LATIN SMALL LETTER C WITH CARON
	0x00E9,	//	LATIN SMALL LETTER E WITH ACUTE
	0x0119,	//	LATIN SMALL LETTER E WITH OGONEK
	0x00EB,	//	LATIN SMALL LETTER E WITH DIAERESIS
	0x011B,	//	LATIN SMALL LETTER E WITH CARON
	0x00ED,	//	LATIN SMALL LETTER I WITH ACUTE
	0x00EE,	//	LATIN SMALL LETTER I WITH CIRCUMFLEX
	0x010F,	//	LATIN SMALL LETTER D WITH CARON
	0x0111,	//	LATIN SMALL LETTER D WITH STROKE
	0x0144,	//	LATIN SMALL LETTER N WITH ACUTE
	0x0148,	//	LATIN SMALL LETTER N WITH CARON
	0x00F3,	//	LATIN SMALL LETTER O WITH ACUTE
	0x00F4,	//	LATIN SMALL LETTER O WITH CIRCUMFLEX
	0x0151,	//	LATIN SMALL LETTER O WITH DOUBLE ACUTE
	0x00F6,	//	LATIN SMALL LETTER O WITH DIAERESIS
	0x00F7,	//	DIVISION SIGN
	0x0159,	//	LATIN SMALL LETTER R WITH CARON
	0x016F,	//	LATIN SMALL LETTER U WITH RING ABOVE
	0x00FA,	//	LATIN SMALL LETTER U WITH ACUTE
	0x0171,	//	LATIN SMALL LETTER U WITH DOUBLE ACUTE
	0x00FC,	//	LATIN SMALL LETTER U WITH DIAERESIS
	0x00FD,	//	LATIN SMALL LETTER Y WITH ACUTE
	0x0163,	//	LATIN SMALL LETTER T WITH CEDILLA
	0x02D9	//	DOT ABOVE
};

static const CharSetCvt::MapEnt to8859_2[] = {
{0x00A0, 0xA0},	//	NO-BREAK SPACE
{0x00A4, 0xA4},	//	CURRENCY SIGN
{0x00A7, 0xA7},	//	SECTION SIGN
{0x00A8, 0xA8},	//	DIAERESIS
{0x00AD, 0xAD},	//	SOFT HYPHEN
{0x00B0, 0xB0},	//	DEGREE SIGN
{0x00B4, 0xB4},	//	ACUTE ACCENT
{0x00B8, 0xB8},	//	CEDILLA
{0x00C1, 0xC1},	//	LATIN CAPITAL LETTER A WITH ACUTE
{0x00C2, 0xC2},	//	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00C4, 0xC4},	//	LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00C7, 0xC7},	//	LATIN CAPITAL LETTER C WITH CEDILLA
{0x00C9, 0xC9},	//	LATIN CAPITAL LETTER E WITH ACUTE
{0x00CB, 0xCB},	//	LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00CD, 0xCD},	//	LATIN CAPITAL LETTER I WITH ACUTE
{0x00CE, 0xCE},	//	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00D3, 0xD3},	//	LATIN CAPITAL LETTER O WITH ACUTE
{0x00D4, 0xD4},	//	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00D6, 0xD6},	//	LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00D7, 0xD7},	//	MULTIPLICATION SIGN
{0x00DA, 0xDA},	//	LATIN CAPITAL LETTER U WITH ACUTE
{0x00DC, 0xDC},	//	LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00DD, 0xDD},	//	LATIN CAPITAL LETTER Y WITH ACUTE
{0x00DF, 0xDF},	//	LATIN SMALL LETTER SHARP S
{0x00E1, 0xE1},	//	LATIN SMALL LETTER A WITH ACUTE
{0x00E2, 0xE2},	//	LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00E4, 0xE4},	//	LATIN SMALL LETTER A WITH DIAERESIS
{0x00E7, 0xE7},	//	LATIN SMALL LETTER C WITH CEDILLA
{0x00E9, 0xE9},	//	LATIN SMALL LETTER E WITH ACUTE
{0x00EB, 0xEB},	//	LATIN SMALL LETTER E WITH DIAERESIS
{0x00ED, 0xED},	//	LATIN SMALL LETTER I WITH ACUTE
{0x00EE, 0xEE},	//	LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00F3, 0xF3},	//	LATIN SMALL LETTER O WITH ACUTE
{0x00F4, 0xF4},	//	LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00F6, 0xF6},	//	LATIN SMALL LETTER O WITH DIAERESIS
{0x00F7, 0xF7},	//	DIVISION SIGN
{0x00FA, 0xFA},	//	LATIN SMALL LETTER U WITH ACUTE
{0x00FC, 0xFC},	//	LATIN SMALL LETTER U WITH DIAERESIS
{0x00FD, 0xFD},	//	LATIN SMALL LETTER Y WITH ACUTE
{0x0102, 0xC3},	//	LATIN CAPITAL LETTER A WITH BREVE
{0x0103, 0xE3},	//	LATIN SMALL LETTER A WITH BREVE
{0x0104, 0xA1},	//	LATIN CAPITAL LETTER A WITH OGONEK
{0x0105, 0xB1},	//	LATIN SMALL LETTER A WITH OGONEK
{0x0106, 0xC6},	//	LATIN CAPITAL LETTER C WITH ACUTE
{0x0107, 0xE6},	//	LATIN SMALL LETTER C WITH ACUTE
{0x010C, 0xC8},	//	LATIN CAPITAL LETTER C WITH CARON
{0x010D, 0xE8},	//	LATIN SMALL LETTER C WITH CARON
{0x010E, 0xCF},	//	LATIN CAPITAL LETTER D WITH CARON
{0x010F, 0xEF},	//	LATIN SMALL LETTER D WITH CARON
{0x0110, 0xD0},	//	LATIN CAPITAL LETTER D WITH STROKE
{0x0111, 0xF0},	//	LATIN SMALL LETTER D WITH STROKE
{0x0118, 0xCA},	//	LATIN CAPITAL LETTER E WITH OGONEK
{0x0119, 0xEA},	//	LATIN SMALL LETTER E WITH OGONEK
{0x011A, 0xCC},	//	LATIN CAPITAL LETTER E WITH CARON
{0x011B, 0xEC},	//	LATIN SMALL LETTER E WITH CARON
{0x0139, 0xC5},	//	LATIN CAPITAL LETTER L WITH ACUTE
{0x013A, 0xE5},	//	LATIN SMALL LETTER L WITH ACUTE
{0x013D, 0xA5},	//	LATIN CAPITAL LETTER L WITH CARON
{0x013E, 0xB5},	//	LATIN SMALL LETTER L WITH CARON
{0x0141, 0xA3},	//	LATIN CAPITAL LETTER L WITH STROKE
{0x0142, 0xB3},	//	LATIN SMALL LETTER L WITH STROKE
{0x0143, 0xD1},	//	LATIN CAPITAL LETTER N WITH ACUTE
{0x0144, 0xF1},	//	LATIN SMALL LETTER N WITH ACUTE
{0x0147, 0xD2},	//	LATIN CAPITAL LETTER N WITH CARON
{0x0148, 0xF2},	//	LATIN SMALL LETTER N WITH CARON
{0x0150, 0xD5},	//	LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
{0x0151, 0xF5},	//	LATIN SMALL LETTER O WITH DOUBLE ACUTE
{0x0154, 0xC0},	//	LATIN CAPITAL LETTER R WITH ACUTE
{0x0155, 0xE0},	//	LATIN SMALL LETTER R WITH ACUTE
{0x0158, 0xD8},	//	LATIN CAPITAL LETTER R WITH CARON
{0x0159, 0xF8},	//	LATIN SMALL LETTER R WITH CARON
{0x015A, 0xA6},	//	LATIN CAPITAL LETTER S WITH ACUTE
{0x015B, 0xB6},	//	LATIN SMALL LETTER S WITH ACUTE
{0x015E, 0xAA},	//	LATIN CAPITAL LETTER S WITH CEDILLA
{0x015F, 0xBA},	//	LATIN SMALL LETTER S WITH CEDILLA
{0x0160, 0xA9},	//	LATIN CAPITAL LETTER S WITH CARON
{0x0161, 0xB9},	//	LATIN SMALL LETTER S WITH CARON
{0x0162, 0xDE},	//	LATIN CAPITAL LETTER T WITH CEDILLA
{0x0163, 0xFE},	//	LATIN SMALL LETTER T WITH CEDILLA
{0x0164, 0xAB},	//	LATIN CAPITAL LETTER T WITH CARON
{0x0165, 0xBB},	//	LATIN SMALL LETTER T WITH CARON
{0x016E, 0xD9},	//	LATIN CAPITAL LETTER U WITH RING ABOVE
{0x016F, 0xF9},	//	LATIN SMALL LETTER U WITH RING ABOVE
{0x0170, 0xDB},	//	LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
{0x0171, 0xFB},	//	LATIN SMALL LETTER U WITH DOUBLE ACUTE
{0x0179, 0xAC},	//	LATIN CAPITAL LETTER Z WITH ACUTE
{0x017A, 0xBC},	//	LATIN SMALL LETTER Z WITH ACUTE
{0x017B, 0xAF},	//	LATIN CAPITAL LETTER Z WITH DOT ABOVE
{0x017C, 0xBF},	//	LATIN SMALL LETTER Z WITH DOT ABOVE
{0x017D, 0xAE},	//	LATIN CAPITAL LETTER Z WITH CARON
{0x017E, 0xBE},	//	LATIN SMALL LETTER Z WITH CARON
{0x02C7, 0xB7},	//	CARON
{0x02D8, 0xA2},	//	BREVE
{0x02D9, 0xFF},	//	DOT ABOVE
{0x02DB, 0xB2},	//	OGONEK
{0x02DD, 0xBD}	//	DOUBLE ACUTE ACCENT
};

static const unsigned short fromCP1250[] = {
	0x20AC,	// EURO SIGN
	0xfffd,	//   	UNDEFINED
	0x201A,	// SINGLE LOW-9 QUOTATION MARK
	0xfffd,	//   	UNDEFINED
	0x201E,	// DOUBLE LOW-9 QUOTATION MARK
	0x2026,	// HORIZONTAL ELLIPSIS
	0x2020,	// DAGGER
	0x2021,	// DOUBLE DAGGER
	0xfffd,	//   	UNDEFINED
	0x2030,	// PER MILLE SIGN
	0x0160,	// LATIN CAPITAL LETTER S WITH CARON
	0x2039,	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
	0x015A,	// LATIN CAPITAL LETTER S WITH ACUTE
	0x0164,	// LATIN CAPITAL LETTER T WITH CARON
	0x017D,	// LATIN CAPITAL LETTER Z WITH CARON
	0x0179,	// LATIN CAPITAL LETTER Z WITH ACUTE
	0xfffd,	//   	UNDEFINED
	0x2018,	// LEFT SINGLE QUOTATION MARK
	0x2019,	// RIGHT SINGLE QUOTATION MARK
	0x201C,	// LEFT DOUBLE QUOTATION MARK
	0x201D,	// RIGHT DOUBLE QUOTATION MARK
	0x2022,	// BULLET
	0x2013,	// EN DASH
	0x2014,	// EM DASH
	0xfffd,	//   	UNDEFINED
	0x2122,	// TRADE MARK SIGN
	0x0161,	// LATIN SMALL LETTER S WITH CARON
	0x203A,	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
	0x015B,	// LATIN SMALL LETTER S WITH ACUTE
	0x0165,	// LATIN SMALL LETTER T WITH CARON
	0x017E,	// LATIN SMALL LETTER Z WITH CARON
	0x017A,	// LATIN SMALL LETTER Z WITH ACUTE
	0x00A0,	// NO-BREAK SPACE
	0x02C7,	// CARON
	0x02D8,	// BREVE
	0x0141,	// LATIN CAPITAL LETTER L WITH STROKE
	0x00A4,	// CURRENCY SIGN
	0x0104,	// LATIN CAPITAL LETTER A WITH OGONEK
	0x00A6,	// BROKEN BAR
	0x00A7,	// SECTION SIGN
	0x00A8,	// DIAERESIS
	0x00A9,	// COPYRIGHT SIGN
	0x015E,	// LATIN CAPITAL LETTER S WITH CEDILLA
	0x00AB,	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
	0x00AC,	// NOT SIGN
	0x00AD,	// SOFT HYPHEN
	0x00AE,	// REGISTERED SIGN
	0x017B,	// LATIN CAPITAL LETTER Z WITH DOT ABOVE
	0x00B0,	// DEGREE SIGN
	0x00B1,	// PLUS-MINUS SIGN
	0x02DB,	// OGONEK
	0x0142,	// LATIN SMALL LETTER L WITH STROKE
	0x00B4,	// ACUTE ACCENT
	0x00B5,	// MICRO SIGN
	0x00B6,	// PILCROW SIGN
	0x00B7,	// MIDDLE DOT
	0x00B8,	// CEDILLA
	0x0105,	// LATIN SMALL LETTER A WITH OGONEK
	0x015F,	// LATIN SMALL LETTER S WITH CEDILLA
	0x00BB,	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
	0x013D,	// LATIN CAPITAL LETTER L WITH CARON
	0x02DD,	// DOUBLE ACUTE ACCENT
	0x013E,	// LATIN SMALL LETTER L WITH CARON
	0x017C,	// LATIN SMALL LETTER Z WITH DOT ABOVE
	0x0154,	// LATIN CAPITAL LETTER R WITH ACUTE
	0x00C1,	// LATIN CAPITAL LETTER A WITH ACUTE
	0x00C2,	// LATIN CAPITAL LETTER A WITH CIRCUMFLEX
	0x0102,	// LATIN CAPITAL LETTER A WITH BREVE
	0x00C4,	// LATIN CAPITAL LETTER A WITH DIAERESIS
	0x0139,	// LATIN CAPITAL LETTER L WITH ACUTE
	0x0106,	// LATIN CAPITAL LETTER C WITH ACUTE
	0x00C7,	// LATIN CAPITAL LETTER C WITH CEDILLA
	0x010C,	// LATIN CAPITAL LETTER C WITH CARON
	0x00C9,	// LATIN CAPITAL LETTER E WITH ACUTE
	0x0118,	// LATIN CAPITAL LETTER E WITH OGONEK
	0x00CB,	// LATIN CAPITAL LETTER E WITH DIAERESIS
	0x011A,	// LATIN CAPITAL LETTER E WITH CARON
	0x00CD,	// LATIN CAPITAL LETTER I WITH ACUTE
	0x00CE,	// LATIN CAPITAL LETTER I WITH CIRCUMFLEX
	0x010E,	// LATIN CAPITAL LETTER D WITH CARON
	0x0110,	// LATIN CAPITAL LETTER D WITH STROKE
	0x0143,	// LATIN CAPITAL LETTER N WITH ACUTE
	0x0147,	// LATIN CAPITAL LETTER N WITH CARON
	0x00D3,	// LATIN CAPITAL LETTER O WITH ACUTE
	0x00D4,	// LATIN CAPITAL LETTER O WITH CIRCUMFLEX
	0x0150,	// LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
	0x00D6,	// LATIN CAPITAL LETTER O WITH DIAERESIS
	0x00D7,	// MULTIPLICATION SIGN
	0x0158,	// LATIN CAPITAL LETTER R WITH CARON
	0x016E,	// LATIN CAPITAL LETTER U WITH RING ABOVE
	0x00DA,	// LATIN CAPITAL LETTER U WITH ACUTE
	0x0170,	// LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
	0x00DC,	// LATIN CAPITAL LETTER U WITH DIAERESIS
	0x00DD,	// LATIN CAPITAL LETTER Y WITH ACUTE
	0x0162,	// LATIN CAPITAL LETTER T WITH CEDILLA
	0x00DF,	// LATIN SMALL LETTER SHARP S
	0x0155,	// LATIN SMALL LETTER R WITH ACUTE
	0x00E1,	// LATIN SMALL LETTER A WITH ACUTE
	0x00E2,	// LATIN SMALL LETTER A WITH CIRCUMFLEX
	0x0103,	// LATIN SMALL LETTER A WITH BREVE
	0x00E4,	// LATIN SMALL LETTER A WITH DIAERESIS
	0x013A,	// LATIN SMALL LETTER L WITH ACUTE
	0x0107,	// LATIN SMALL LETTER C WITH ACUTE
	0x00E7,	// LATIN SMALL LETTER C WITH CEDILLA
	0x010D,	// LATIN SMALL LETTER C WITH CARON
	0x00E9,	// LATIN SMALL LETTER E WITH ACUTE
	0x0119,	// LATIN SMALL LETTER E WITH OGONEK
	0x00EB,	// LATIN SMALL LETTER E WITH DIAERESIS
	0x011B,	// LATIN SMALL LETTER E WITH CARON
	0x00ED,	// LATIN SMALL LETTER I WITH ACUTE
	0x00EE,	// LATIN SMALL LETTER I WITH CIRCUMFLEX
	0x010F,	// LATIN SMALL LETTER D WITH CARON
	0x0111,	// LATIN SMALL LETTER D WITH STROKE
	0x0144,	// LATIN SMALL LETTER N WITH ACUTE
	0x0148,	// LATIN SMALL LETTER N WITH CARON
	0x00F3,	// LATIN SMALL LETTER O WITH ACUTE
	0x00F4,	// LATIN SMALL LETTER O WITH CIRCUMFLEX
	0x0151,	// LATIN SMALL LETTER O WITH DOUBLE ACUTE
	0x00F6,	// LATIN SMALL LETTER O WITH DIAERESIS
	0x00F7,	// DIVISION SIGN
	0x0159,	// LATIN SMALL LETTER R WITH CARON
	0x016F,	// LATIN SMALL LETTER U WITH RING ABOVE
	0x00FA,	// LATIN SMALL LETTER U WITH ACUTE
	0x0171,	// LATIN SMALL LETTER U WITH DOUBLE ACUTE
	0x00FC,	// LATIN SMALL LETTER U WITH DIAERESIS
	0x00FD,	// LATIN SMALL LETTER Y WITH ACUTE
	0x0163,	// LATIN SMALL LETTER T WITH CEDILLA
	0x02D9,	// DOT ABOVE
};

static const CharSetCvt::MapEnt toCP1250[] = {
{0x00A0, 0xA0},	// NO-BREAK SPACE
{0x00A4, 0xA4},	// CURRENCY SIGN
{0x00A6, 0xA6},	// BROKEN BAR
{0x00A7, 0xA7},	// SECTION SIGN
{0x00A8, 0xA8},	// DIAERESIS
{0x00A9, 0xA9},	// COPYRIGHT SIGN
{0x00AB, 0xAB},	// LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00AC, 0xAC},	// NOT SIGN
{0x00AD, 0xAD},	// SOFT HYPHEN
{0x00AE, 0xAE},	// REGISTERED SIGN
{0x00B0, 0xB0},	// DEGREE SIGN
{0x00B1, 0xB1},	// PLUS-MINUS SIGN
{0x00B4, 0xB4},	// ACUTE ACCENT
{0x00B5, 0xB5},	// MICRO SIGN
{0x00B6, 0xB6},	// PILCROW SIGN
{0x00B7, 0xB7},	// MIDDLE DOT
{0x00B8, 0xB8},	// CEDILLA
{0x00BB, 0xBB},	// RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00C1, 0xC1},	// LATIN CAPITAL LETTER A WITH ACUTE
{0x00C2, 0xC2},	// LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00C4, 0xC4},	// LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00C7, 0xC7},	// LATIN CAPITAL LETTER C WITH CEDILLA
{0x00C9, 0xC9},	// LATIN CAPITAL LETTER E WITH ACUTE
{0x00CB, 0xCB},	// LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00CD, 0xCD},	// LATIN CAPITAL LETTER I WITH ACUTE
{0x00CE, 0xCE},	// LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00D3, 0xD3},	// LATIN CAPITAL LETTER O WITH ACUTE
{0x00D4, 0xD4},	// LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00D6, 0xD6},	// LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00D7, 0xD7},	// MULTIPLICATION SIGN
{0x00DA, 0xDA},	// LATIN CAPITAL LETTER U WITH ACUTE
{0x00DC, 0xDC},	// LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00DD, 0xDD},	// LATIN CAPITAL LETTER Y WITH ACUTE
{0x00DF, 0xDF},	// LATIN SMALL LETTER SHARP S
{0x00E1, 0xE1},	// LATIN SMALL LETTER A WITH ACUTE
{0x00E2, 0xE2},	// LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00E4, 0xE4},	// LATIN SMALL LETTER A WITH DIAERESIS
{0x00E7, 0xE7},	// LATIN SMALL LETTER C WITH CEDILLA
{0x00E9, 0xE9},	// LATIN SMALL LETTER E WITH ACUTE
{0x00EB, 0xEB},	// LATIN SMALL LETTER E WITH DIAERESIS
{0x00ED, 0xED},	// LATIN SMALL LETTER I WITH ACUTE
{0x00EE, 0xEE},	// LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00F3, 0xF3},	// LATIN SMALL LETTER O WITH ACUTE
{0x00F4, 0xF4},	// LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00F6, 0xF6},	// LATIN SMALL LETTER O WITH DIAERESIS
{0x00F7, 0xF7},	// DIVISION SIGN
{0x00FA, 0xFA},	// LATIN SMALL LETTER U WITH ACUTE
{0x00FC, 0xFC},	// LATIN SMALL LETTER U WITH DIAERESIS
{0x00FD, 0xFD},	// LATIN SMALL LETTER Y WITH ACUTE
{0x0102, 0xC3},	// LATIN CAPITAL LETTER A WITH BREVE
{0x0103, 0xE3},	// LATIN SMALL LETTER A WITH BREVE
{0x0104, 0xA5},	// LATIN CAPITAL LETTER A WITH OGONEK
{0x0105, 0xB9},	// LATIN SMALL LETTER A WITH OGONEK
{0x0106, 0xC6},	// LATIN CAPITAL LETTER C WITH ACUTE
{0x0107, 0xE6},	// LATIN SMALL LETTER C WITH ACUTE
{0x010C, 0xC8},	// LATIN CAPITAL LETTER C WITH CARON
{0x010D, 0xE8},	// LATIN SMALL LETTER C WITH CARON
{0x010E, 0xCF},	// LATIN CAPITAL LETTER D WITH CARON
{0x010F, 0xEF},	// LATIN SMALL LETTER D WITH CARON
{0x0110, 0xD0},	// LATIN CAPITAL LETTER D WITH STROKE
{0x0111, 0xF0},	// LATIN SMALL LETTER D WITH STROKE
{0x0118, 0xCA},	// LATIN CAPITAL LETTER E WITH OGONEK
{0x0119, 0xEA},	// LATIN SMALL LETTER E WITH OGONEK
{0x011A, 0xCC},	// LATIN CAPITAL LETTER E WITH CARON
{0x011B, 0xEC},	// LATIN SMALL LETTER E WITH CARON
{0x0139, 0xC5},	// LATIN CAPITAL LETTER L WITH ACUTE
{0x013A, 0xE5},	// LATIN SMALL LETTER L WITH ACUTE
{0x013D, 0xBC},	// LATIN CAPITAL LETTER L WITH CARON
{0x013E, 0xBE},	// LATIN SMALL LETTER L WITH CARON
{0x0141, 0xA3},	// LATIN CAPITAL LETTER L WITH STROKE
{0x0142, 0xB3},	// LATIN SMALL LETTER L WITH STROKE
{0x0143, 0xD1},	// LATIN CAPITAL LETTER N WITH ACUTE
{0x0144, 0xF1},	// LATIN SMALL LETTER N WITH ACUTE
{0x0147, 0xD2},	// LATIN CAPITAL LETTER N WITH CARON
{0x0148, 0xF2},	// LATIN SMALL LETTER N WITH CARON
{0x0150, 0xD5},	// LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
{0x0151, 0xF5},	// LATIN SMALL LETTER O WITH DOUBLE ACUTE
{0x0154, 0xC0},	// LATIN CAPITAL LETTER R WITH ACUTE
{0x0155, 0xE0},	// LATIN SMALL LETTER R WITH ACUTE
{0x0158, 0xD8},	// LATIN CAPITAL LETTER R WITH CARON
{0x0159, 0xF8},	// LATIN SMALL LETTER R WITH CARON
{0x015A, 0x8C},	// LATIN CAPITAL LETTER S WITH ACUTE
{0x015B, 0x9C},	// LATIN SMALL LETTER S WITH ACUTE
{0x015E, 0xAA},	// LATIN CAPITAL LETTER S WITH CEDILLA
{0x015F, 0xBA},	// LATIN SMALL LETTER S WITH CEDILLA
{0x0160, 0x8A},	// LATIN CAPITAL LETTER S WITH CARON
{0x0161, 0x9A},	// LATIN SMALL LETTER S WITH CARON
{0x0162, 0xDE},	// LATIN CAPITAL LETTER T WITH CEDILLA
{0x0163, 0xFE},	// LATIN SMALL LETTER T WITH CEDILLA
{0x0164, 0x8D},	// LATIN CAPITAL LETTER T WITH CARON
{0x0165, 0x9D},	// LATIN SMALL LETTER T WITH CARON
{0x016E, 0xD9},	// LATIN CAPITAL LETTER U WITH RING ABOVE
{0x016F, 0xF9},	// LATIN SMALL LETTER U WITH RING ABOVE
{0x0170, 0xDB},	// LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
{0x0171, 0xFB},	// LATIN SMALL LETTER U WITH DOUBLE ACUTE
{0x0179, 0x8F},	// LATIN CAPITAL LETTER Z WITH ACUTE
{0x017A, 0x9F},	// LATIN SMALL LETTER Z WITH ACUTE
{0x017B, 0xAF},	// LATIN CAPITAL LETTER Z WITH DOT ABOVE
{0x017C, 0xBF},	// LATIN SMALL LETTER Z WITH DOT ABOVE
{0x017D, 0x8E},	// LATIN CAPITAL LETTER Z WITH CARON
{0x017E, 0x9E},	// LATIN SMALL LETTER Z WITH CARON
{0x02C7, 0xA1},	// CARON
{0x02D8, 0xA2},	// BREVE
{0x02D9, 0xFF},	// DOT ABOVE
{0x02DB, 0xB2},	// OGONEK
{0x02DD, 0xBD},	// DOUBLE ACUTE ACCENT
{0x2013, 0x96},	// EN DASH
{0x2014, 0x97},	// EM DASH
{0x2018, 0x91},	// LEFT SINGLE QUOTATION MARK
{0x2019, 0x92},	// RIGHT SINGLE QUOTATION MARK
{0x201A, 0x82},	// SINGLE LOW-9 QUOTATION MARK
{0x201C, 0x93},	// LEFT DOUBLE QUOTATION MARK
{0x201D, 0x94},	// RIGHT DOUBLE QUOTATION MARK
{0x201E, 0x84},	// DOUBLE LOW-9 QUOTATION MARK
{0x2020, 0x86},	// DAGGER
{0x2021, 0x87},	// DOUBLE DAGGER
{0x2022, 0x95},	// BULLET
{0x2026, 0x85},	// HORIZONTAL ELLIPSIS
{0x2030, 0x89},	// PER MILLE SIGN
{0x2039, 0x8B},	// SINGLE LEFT-POINTING ANGLE QUOTATION MARK
{0x203A, 0x9B},	// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
{0x20AC, 0x80},	// EURO SIGN
{0x2122, 0x99}	// TRADE MARK SIGN
};

const static unsigned short from852[] = {
	0x00c7,	//LATIN CAPITAL LETTER C WITH CEDILLA
	0x00fc,	//LATIN SMALL LETTER U WITH DIAERESIS
	0x00e9,	//LATIN SMALL LETTER E WITH ACUTE
	0x00e2,	//LATIN SMALL LETTER A WITH CIRCUMFLEX
	0x00e4,	//LATIN SMALL LETTER A WITH DIAERESIS
	0x016f,	//LATIN SMALL LETTER U WITH RING ABOVE
	0x0107,	//LATIN SMALL LETTER C WITH ACUTE
	0x00e7,	//LATIN SMALL LETTER C WITH CEDILLA
	0x0142,	//LATIN SMALL LETTER L WITH STROKE
	0x00eb,	//LATIN SMALL LETTER E WITH DIAERESIS
	0x0150,	//LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
	0x0151,	//LATIN SMALL LETTER O WITH DOUBLE ACUTE
	0x00ee,	//LATIN SMALL LETTER I WITH CIRCUMFLEX
	0x0179,	//LATIN CAPITAL LETTER Z WITH ACUTE
	0x00c4,	//LATIN CAPITAL LETTER A WITH DIAERESIS
	0x0106,	//LATIN CAPITAL LETTER C WITH ACUTE
	0x00c9,	//LATIN CAPITAL LETTER E WITH ACUTE
	0x0139,	//LATIN CAPITAL LETTER L WITH ACUTE
	0x013a,	//LATIN SMALL LETTER L WITH ACUTE
	0x00f4,	//LATIN SMALL LETTER O WITH CIRCUMFLEX
	0x00f6,	//LATIN SMALL LETTER O WITH DIAERESIS
	0x013d,	//LATIN CAPITAL LETTER L WITH CARON
	0x013e,	//LATIN SMALL LETTER L WITH CARON
	0x015a,	//LATIN CAPITAL LETTER S WITH ACUTE
	0x015b,	//LATIN SMALL LETTER S WITH ACUTE
	0x00d6,	//LATIN CAPITAL LETTER O WITH DIAERESIS
	0x00dc,	//LATIN CAPITAL LETTER U WITH DIAERESIS
	0x0164,	//LATIN CAPITAL LETTER T WITH CARON
	0x0165,	//LATIN SMALL LETTER T WITH CARON
	0x0141,	//LATIN CAPITAL LETTER L WITH STROKE
	0x00d7,	//MULTIPLICATION SIGN
	0x010d,	//LATIN SMALL LETTER C WITH CARON
	0x00e1,	//LATIN SMALL LETTER A WITH ACUTE
	0x00ed,	//LATIN SMALL LETTER I WITH ACUTE
	0x00f3,	//LATIN SMALL LETTER O WITH ACUTE
	0x00fa,	//LATIN SMALL LETTER U WITH ACUTE
	0x0104,	//LATIN CAPITAL LETTER A WITH OGONEK
	0x0105,	//LATIN SMALL LETTER A WITH OGONEK
	0x017d,	//LATIN CAPITAL LETTER Z WITH CARON
	0x017e,	//LATIN SMALL LETTER Z WITH CARON
	0x0118,	//LATIN CAPITAL LETTER E WITH OGONEK
	0x0119,	//LATIN SMALL LETTER E WITH OGONEK
	0x00ac,	//NOT SIGN
	0x017a,	//LATIN SMALL LETTER Z WITH ACUTE
	0x010c,	//LATIN CAPITAL LETTER C WITH CARON
	0x015f,	//LATIN SMALL LETTER S WITH CEDILLA
	0x00ab,	//LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
	0x00bb,	//RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
	0x2591,	//LIGHT SHADE
	0x2592,	//MEDIUM SHADE
	0x2593,	//DARK SHADE
	0x2502,	//BOX DRAWINGS LIGHT VERTICAL
	0x2524,	//BOX DRAWINGS LIGHT VERTICAL AND LEFT
	0x00c1,	//LATIN CAPITAL LETTER A WITH ACUTE
	0x00c2,	//LATIN CAPITAL LETTER A WITH CIRCUMFLEX
	0x011a,	//LATIN CAPITAL LETTER E WITH CARON
	0x015e,	//LATIN CAPITAL LETTER S WITH CEDILLA
	0x2563,	//BOX DRAWINGS DOUBLE VERTICAL AND LEFT
	0x2551,	//BOX DRAWINGS DOUBLE VERTICAL
	0x2557,	//BOX DRAWINGS DOUBLE DOWN AND LEFT
	0x255d,	//BOX DRAWINGS DOUBLE UP AND LEFT
	0x017b,	//LATIN CAPITAL LETTER Z WITH DOT ABOVE
	0x017c,	//LATIN SMALL LETTER Z WITH DOT ABOVE
	0x2510,	//BOX DRAWINGS LIGHT DOWN AND LEFT
	0x2514,	//BOX DRAWINGS LIGHT UP AND RIGHT
	0x2534,	//BOX DRAWINGS LIGHT UP AND HORIZONTAL
	0x252c,	//BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
	0x251c,	//BOX DRAWINGS LIGHT VERTICAL AND RIGHT
	0x2500,	//BOX DRAWINGS LIGHT HORIZONTAL
	0x253c,	//BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
	0x0102,	//LATIN CAPITAL LETTER A WITH BREVE
	0x0103,	//LATIN SMALL LETTER A WITH BREVE
	0x255a,	//BOX DRAWINGS DOUBLE UP AND RIGHT
	0x2554,	//BOX DRAWINGS DOUBLE DOWN AND RIGHT
	0x2569,	//BOX DRAWINGS DOUBLE UP AND HORIZONTAL
	0x2566,	//BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
	0x2560,	//BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
	0x2550,	//BOX DRAWINGS DOUBLE HORIZONTAL
	0x256c,	//BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
	0x00a4,	//CURRENCY SIGN
	0x0111,	//LATIN SMALL LETTER D WITH STROKE
	0x0110,	//LATIN CAPITAL LETTER D WITH STROKE
	0x010e,	//LATIN CAPITAL LETTER D WITH CARON
	0x00cb,	//LATIN CAPITAL LETTER E WITH DIAERESIS
	0x010f,	//LATIN SMALL LETTER D WITH CARON
	0x0147,	//LATIN CAPITAL LETTER N WITH CARON
	0x00cd,	//LATIN CAPITAL LETTER I WITH ACUTE
	0x00ce,	//LATIN CAPITAL LETTER I WITH CIRCUMFLEX
	0x011b,	//LATIN SMALL LETTER E WITH CARON
	0x2518,	//BOX DRAWINGS LIGHT UP AND LEFT
	0x250c,	//BOX DRAWINGS LIGHT DOWN AND RIGHT
	0x2588,	//FULL BLOCK
	0x2584,	//LOWER HALF BLOCK
	0x0162,	//LATIN CAPITAL LETTER T WITH CEDILLA
	0x016e,	//LATIN CAPITAL LETTER U WITH RING ABOVE
	0x2580,	//UPPER HALF BLOCK
	0x00d3,	//LATIN CAPITAL LETTER O WITH ACUTE
	0x00df,	//LATIN SMALL LETTER SHARP S
	0x00d4,	//LATIN CAPITAL LETTER O WITH CIRCUMFLEX
	0x0143,	//LATIN CAPITAL LETTER N WITH ACUTE
	0x0144,	//LATIN SMALL LETTER N WITH ACUTE
	0x0148,	//LATIN SMALL LETTER N WITH CARON
	0x0160,	//LATIN CAPITAL LETTER S WITH CARON
	0x0161,	//LATIN SMALL LETTER S WITH CARON
	0x0154,	//LATIN CAPITAL LETTER R WITH ACUTE
	0x00da,	//LATIN CAPITAL LETTER U WITH ACUTE
	0x0155,	//LATIN SMALL LETTER R WITH ACUTE
	0x0170,	//LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
	0x00fd,	//LATIN SMALL LETTER Y WITH ACUTE
	0x00dd,	//LATIN CAPITAL LETTER Y WITH ACUTE
	0x0163,	//LATIN SMALL LETTER T WITH CEDILLA
	0x00b4,	//ACUTE ACCENT
	0x00ad,	//SOFT HYPHEN
	0x02dd,	//DOUBLE ACUTE ACCENT
	0x02db,	//OGONEK
	0x02c7,	//CARON
	0x02d8,	//BREVE
	0x00a7,	//SECTION SIGN
	0x00f7,	//DIVISION SIGN
	0x00b8,	//CEDILLA
	0x00b0,	//DEGREE SIGN
	0x00a8,	//DIAERESIS
	0x02d9,	//DOT ABOVE
	0x0171,	//LATIN SMALL LETTER U WITH DOUBLE ACUTE
	0x0158,	//LATIN CAPITAL LETTER R WITH CARON
	0x0159,	//LATIN SMALL LETTER R WITH CARON
	0x25a0,	//BLACK SQUARE
	0x00a0	//NO-BREAK SPACE
};

static const CharSetCvt::MapEnt to852[] = {
{0x00a0, 0xff},	//NO-BREAK SPACE
{0x00a4, 0xcf},	//CURRENCY SIGN
{0x00a7, 0xf5},	//SECTION SIGN
{0x00a8, 0xf9},	//DIAERESIS
{0x00ab, 0xae},	//LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00ac, 0xaa},	//NOT SIGN
{0x00ad, 0xf0},	//SOFT HYPHEN
{0x00b0, 0xf8},	//DEGREE SIGN
{0x00b4, 0xef},	//ACUTE ACCENT
{0x00b8, 0xf7},	//CEDILLA
{0x00bb, 0xaf},	//RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
{0x00c1, 0xb5},	//LATIN CAPITAL LETTER A WITH ACUTE
{0x00c2, 0xb6},	//LATIN CAPITAL LETTER A WITH CIRCUMFLEX
{0x00c4, 0x8e},	//LATIN CAPITAL LETTER A WITH DIAERESIS
{0x00c7, 0x80},	//LATIN CAPITAL LETTER C WITH CEDILLA
{0x00c9, 0x90},	//LATIN CAPITAL LETTER E WITH ACUTE
{0x00cb, 0xd3},	//LATIN CAPITAL LETTER E WITH DIAERESIS
{0x00cd, 0xd6},	//LATIN CAPITAL LETTER I WITH ACUTE
{0x00ce, 0xd7},	//LATIN CAPITAL LETTER I WITH CIRCUMFLEX
{0x00d3, 0xe0},	//LATIN CAPITAL LETTER O WITH ACUTE
{0x00d4, 0xe2},	//LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{0x00d6, 0x99},	//LATIN CAPITAL LETTER O WITH DIAERESIS
{0x00d7, 0x9e},	//MULTIPLICATION SIGN
{0x00da, 0xe9},	//LATIN CAPITAL LETTER U WITH ACUTE
{0x00dc, 0x9a},	//LATIN CAPITAL LETTER U WITH DIAERESIS
{0x00dd, 0xed},	//LATIN CAPITAL LETTER Y WITH ACUTE
{0x00df, 0xe1},	//LATIN SMALL LETTER SHARP S
{0x00e1, 0xa0},	//LATIN SMALL LETTER A WITH ACUTE
{0x00e2, 0x83},	//LATIN SMALL LETTER A WITH CIRCUMFLEX
{0x00e4, 0x84},	//LATIN SMALL LETTER A WITH DIAERESIS
{0x00e7, 0x87},	//LATIN SMALL LETTER C WITH CEDILLA
{0x00e9, 0x82},	//LATIN SMALL LETTER E WITH ACUTE
{0x00eb, 0x89},	//LATIN SMALL LETTER E WITH DIAERESIS
{0x00ed, 0xa1},	//LATIN SMALL LETTER I WITH ACUTE
{0x00ee, 0x8c},	//LATIN SMALL LETTER I WITH CIRCUMFLEX
{0x00f3, 0xa2},	//LATIN SMALL LETTER O WITH ACUTE
{0x00f4, 0x93},	//LATIN SMALL LETTER O WITH CIRCUMFLEX
{0x00f6, 0x94},	//LATIN SMALL LETTER O WITH DIAERESIS
{0x00f7, 0xf6},	//DIVISION SIGN
{0x00fa, 0xa3},	//LATIN SMALL LETTER U WITH ACUTE
{0x00fc, 0x81},	//LATIN SMALL LETTER U WITH DIAERESIS
{0x00fd, 0xec},	//LATIN SMALL LETTER Y WITH ACUTE
{0x0102, 0xc6},	//LATIN CAPITAL LETTER A WITH BREVE
{0x0103, 0xc7},	//LATIN SMALL LETTER A WITH BREVE
{0x0104, 0xa4},	//LATIN CAPITAL LETTER A WITH OGONEK
{0x0105, 0xa5},	//LATIN SMALL LETTER A WITH OGONEK
{0x0106, 0x8f},	//LATIN CAPITAL LETTER C WITH ACUTE
{0x0107, 0x86},	//LATIN SMALL LETTER C WITH ACUTE
{0x010c, 0xac},	//LATIN CAPITAL LETTER C WITH CARON
{0x010d, 0x9f},	//LATIN SMALL LETTER C WITH CARON
{0x010e, 0xd2},	//LATIN CAPITAL LETTER D WITH CARON
{0x010f, 0xd4},	//LATIN SMALL LETTER D WITH CARON
{0x0110, 0xd1},	//LATIN CAPITAL LETTER D WITH STROKE
{0x0111, 0xd0},	//LATIN SMALL LETTER D WITH STROKE
{0x0118, 0xa8},	//LATIN CAPITAL LETTER E WITH OGONEK
{0x0119, 0xa9},	//LATIN SMALL LETTER E WITH OGONEK
{0x011a, 0xb7},	//LATIN CAPITAL LETTER E WITH CARON
{0x011b, 0xd8},	//LATIN SMALL LETTER E WITH CARON
{0x0139, 0x91},	//LATIN CAPITAL LETTER L WITH ACUTE
{0x013a, 0x92},	//LATIN SMALL LETTER L WITH ACUTE
{0x013d, 0x95},	//LATIN CAPITAL LETTER L WITH CARON
{0x013e, 0x96},	//LATIN SMALL LETTER L WITH CARON
{0x0141, 0x9d},	//LATIN CAPITAL LETTER L WITH STROKE
{0x0142, 0x88},	//LATIN SMALL LETTER L WITH STROKE
{0x0143, 0xe3},	//LATIN CAPITAL LETTER N WITH ACUTE
{0x0144, 0xe4},	//LATIN SMALL LETTER N WITH ACUTE
{0x0147, 0xd5},	//LATIN CAPITAL LETTER N WITH CARON
{0x0148, 0xe5},	//LATIN SMALL LETTER N WITH CARON
{0x0150, 0x8a},	//LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
{0x0151, 0x8b},	//LATIN SMALL LETTER O WITH DOUBLE ACUTE
{0x0154, 0xe8},	//LATIN CAPITAL LETTER R WITH ACUTE
{0x0155, 0xea},	//LATIN SMALL LETTER R WITH ACUTE
{0x0158, 0xfc},	//LATIN CAPITAL LETTER R WITH CARON
{0x0159, 0xfd},	//LATIN SMALL LETTER R WITH CARON
{0x015a, 0x97},	//LATIN CAPITAL LETTER S WITH ACUTE
{0x015b, 0x98},	//LATIN SMALL LETTER S WITH ACUTE
{0x015e, 0xb8},	//LATIN CAPITAL LETTER S WITH CEDILLA
{0x015f, 0xad},	//LATIN SMALL LETTER S WITH CEDILLA
{0x0160, 0xe6},	//LATIN CAPITAL LETTER S WITH CARON
{0x0161, 0xe7},	//LATIN SMALL LETTER S WITH CARON
{0x0162, 0xdd},	//LATIN CAPITAL LETTER T WITH CEDILLA
{0x0163, 0xee},	//LATIN SMALL LETTER T WITH CEDILLA
{0x0164, 0x9b},	//LATIN CAPITAL LETTER T WITH CARON
{0x0165, 0x9c},	//LATIN SMALL LETTER T WITH CARON
{0x016e, 0xde},	//LATIN CAPITAL LETTER U WITH RING ABOVE
{0x016f, 0x85},	//LATIN SMALL LETTER U WITH RING ABOVE
{0x0170, 0xeb},	//LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
{0x0171, 0xfb},	//LATIN SMALL LETTER U WITH DOUBLE ACUTE
{0x0179, 0x8d},	//LATIN CAPITAL LETTER Z WITH ACUTE
{0x017a, 0xab},	//LATIN SMALL LETTER Z WITH ACUTE
{0x017b, 0xbd},	//LATIN CAPITAL LETTER Z WITH DOT ABOVE
{0x017c, 0xbe},	//LATIN SMALL LETTER Z WITH DOT ABOVE
{0x017d, 0xa6},	//LATIN CAPITAL LETTER Z WITH CARON
{0x017e, 0xa7},	//LATIN SMALL LETTER Z WITH CARON
{0x02c7, 0xf3},	//CARON
{0x02d8, 0xf4},	//BREVE
{0x02d9, 0xfa},	//DOT ABOVE
{0x02db, 0xf2},	//OGONEK
{0x02dd, 0xf1},	//DOUBLE ACUTE ACCENT
{0x2500, 0xc4},	//BOX DRAWINGS LIGHT HORIZONTAL
{0x2502, 0xb3},	//BOX DRAWINGS LIGHT VERTICAL
{0x250c, 0xda},	//BOX DRAWINGS LIGHT DOWN AND RIGHT
{0x2510, 0xbf},	//BOX DRAWINGS LIGHT DOWN AND LEFT
{0x2514, 0xc0},	//BOX DRAWINGS LIGHT UP AND RIGHT
{0x2518, 0xd9},	//BOX DRAWINGS LIGHT UP AND LEFT
{0x251c, 0xc3},	//BOX DRAWINGS LIGHT VERTICAL AND RIGHT
{0x2524, 0xb4},	//BOX DRAWINGS LIGHT VERTICAL AND LEFT
{0x252c, 0xc2},	//BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
{0x2534, 0xc1},	//BOX DRAWINGS LIGHT UP AND HORIZONTAL
{0x253c, 0xc5},	//BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
{0x2550, 0xcd},	//BOX DRAWINGS DOUBLE HORIZONTAL
{0x2551, 0xba},	//BOX DRAWINGS DOUBLE VERTICAL
{0x2554, 0xc9},	//BOX DRAWINGS DOUBLE DOWN AND RIGHT
{0x2557, 0xbb},	//BOX DRAWINGS DOUBLE DOWN AND LEFT
{0x255a, 0xc8},	//BOX DRAWINGS DOUBLE UP AND RIGHT
{0x255d, 0xbc},	//BOX DRAWINGS DOUBLE UP AND LEFT
{0x2560, 0xcc},	//BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
{0x2563, 0xb9},	//BOX DRAWINGS DOUBLE VERTICAL AND LEFT
{0x2566, 0xcb},	//BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
{0x2569, 0xca},	//BOX DRAWINGS DOUBLE UP AND HORIZONTAL
{0x256c, 0xce},	//BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
{0x2580, 0xdf},	//UPPER HALF BLOCK
{0x2584, 0xdc},	//LOWER HALF BLOCK
{0x2588, 0xdb},	//FULL BLOCK
{0x2591, 0xb0},	//LIGHT SHADE
{0x2592, 0xb1},	//MEDIUM SHADE
{0x2593, 0xb2},	//DARK SHADE
{0x25a0, 0xfe}	//BLACK SQUARE
};

static const struct SimpleCharSet simples[] = {
	{ to437, 128, from437, 0x80 },
	{ toMACROMAN, 128, fromMACROMAN, 0x80 },
	{ to8859_15, 96, from8859_15, 0xa0 },
	{ to8859_5, 96, from8859_5, 0xa0 },
	{ toKOI8_R, 128, fromKOI8_R, 0x80 },
	{ toCP1251, 127, fromCP1251, 0x80 },
	{ toCP1252, 123, fromCP1252, 0x80 },
	{ to850, 128, from850, 0x80 },
	{ to858, 128, from858, 0x80 },
	{ toCP1253, 111, fromCP1253, 0x80 },
	{ to8859_7, 93, from8859_7, 0xa0 },
	{ to737, 128, from737, 0x80 },
	{ toCP1250, 123, fromCP1250, 0x80 },
	{ to852, 128, from852, 0x80 },
	{ to8859_2, 96, from8859_2, 0xa0 }	
};

CharSetCvtUTF8toSimple::CharSetCvtUTF8toSimple(int i)
    : charinfo(simples+i) {}

CharSetCvtSimpletoUTF8::CharSetCvtSimpletoUTF8(int i)
    : charinfo(simples+i) {}

int
CharSetCvtUTF8to8859_1::Cvt(const char **sourcestart, const char *sourceend,
	       char **targetstart, char *targetend)
{
	unsigned int v;

	while (*sourcestart < sourceend && *targetstart < targetend)
	{
	    v = **sourcestart & 0xff;
	    if (v & 0x80)
	    {
		int l = bytesFromUTF8[v];

		if (l + *sourcestart >= sourceend)
		{
		    lasterr = PARTIALCHAR;
		    break;
		}
		if (v == 0xc2)
		    **targetstart = *++*sourcestart;
		else if (v == 0xc3)
		    **targetstart = *++*sourcestart + 0x40;
		else if (checkBOM && v == 0xef &&
			((*sourcestart)[1] & 0xff) == 0xbb &&
			((*sourcestart)[2] & 0xff) == 0xbf)
		{
		    checkBOM = 0;
		    *sourcestart += 3;
		    continue;	// suppress BOM
		}
		else // out of range
		{
		    lasterr = NOMAPPING;
		    break;
		}
# ifdef STRICT_UTF8
		if (**targetstart > 0)
		{
		    // this should have been a high-bit byte
		    lasterr = NOMAPPING;
		    --*sourcestart;
		    break;
		}
# endif
	    }
	    else
	    {
		**targetstart = v;
	    }
	    ++charcnt;
	    if( v == '\n' ) {
		++linecnt;
		charcnt = 0;
	    }
	    ++*sourcestart;
	    ++*targetstart;
	    checkBOM = 0;
	}
	return 0;
}

CharSetCvt *
CharSetCvt8859_1toUTF8::Clone()
{
	return new CharSetCvt8859_1toUTF8;
}

CharSetCvt *
CharSetCvt8859_1toUTF8::ReverseCvt()
{
	return new CharSetCvtUTF8to8859_1;
}

int
CharSetCvt8859_1toUTF8::Cvt(const char **sourcestart, const char *sourceend,
	       char **targetstart, char *targetend)
{
	unsigned int v;

	while (*sourcestart < sourceend && *targetstart < targetend)
	{
	    v = **sourcestart & 0xff;
	    if (v & 0x80)
	    {
		if (1 + *targetstart == targetend)
		{
		    lasterr = PARTIALCHAR;
		    break;
		}
		if (v >= 0xc0)
		{
		    **targetstart = (char)0xc3;
		    v -= 0x40;
		}
		else
		    **targetstart = (char)0xc2;
		*++*targetstart = v;
	    }
	    else
		**targetstart = v;
	    ++charcnt;
	    if( v == '\n' ) {
		++linecnt;
		charcnt = 0;
	    }
	    ++*targetstart;
	    ++*sourcestart;
	}
	return 0;
}

CharSetCvt *
CharSetCvtUTF8to8859_1::Clone()
{
	return new CharSetCvtUTF8to8859_1;
}

CharSetCvt *
CharSetCvtUTF8to8859_1::ReverseCvt()
{
	return new CharSetCvt8859_1toUTF8;
}
