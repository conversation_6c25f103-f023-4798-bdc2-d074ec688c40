/*
 * Copyright 1995, 2018 Perforce Software.  All rights reserved.
 *
 * This file is part of Perforce - the FAST SCM System.
 */

/*
 * Perforce client legal compliance statements
 *
 */

# include <stdhdrs.h>

# include <strbuf.h>
# include <strdict.h>
# include <strtable.h>
# include <error.h>
# include <rpc.h>

# include <filesys.h>
# include <handler.h>

# include "client.h"
# include "clientuser.h"

static ErrorId LegalHelp = { ErrorOf( 0, 0, E_INFO, 0, 0 ),
"\n"
"    The following are the license statements for code used in\n"
"    this program.\n"
"\n"
"    See 'p4 help legal' for additional information.\n"
"\n"
"    P4/P4API License\n"
"    -----------------------\n"
"    Copyright (c) 1995-" ID_Y ", Perforce Software, Inc.\n"
"    All rights reserved.\n"
"\n"
"    Redistribution and use in source and binary forms, with or without\n"
"    modification, are permitted provided that the following conditions are met:\n"
"\n"
"        Redistributions of source code must retain the above copyright notice,\n"
"        this list of conditions and the following disclaimer.\n"
"\n"
"        Redistributions in binary form must reproduce the above copyright\n"
"        notice, this list of conditions and the following disclaimer in the\n"
"        documentation and/or other materials provided with the distribution.\n"
"\n"
"    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n"
"    AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n"
"    IMPLIED  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n"
"    ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n"
"    LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n"
"    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n"
"    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n"
"    INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n"
"    CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n"
"    ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n"
"    POSSIBILITY OF SUCH DAMAGE.\n"
"\n"
"\n"
"    OpenSSL:\n"
"    -----------------------\n"
"	This product includes cryptographic software written by Eric Young\n"
"	(<EMAIL>).\n"
"	This product includes software written by Tim Hudson\n"
"	(<EMAIL>).\n"
"\n"
"    OpenSSL License\n"
"    -----------------------\n"
"\n"
"     * ====================================================================\n"
"     * Copyright (c) 1998-2011 The OpenSSL Project.  All rights reserved.\n"
"     *\n"
"     * Redistribution and use in source and binary forms, with or without\n"
"     * modification, are permitted provided that the following conditions\n"
"     * are met:\n"
"     *\n"
"     * 1. Redistributions of source code must retain the above copyright\n"
"     *    notice, this list of conditions and the following disclaimer.\n"
"     *\n"
"     * 2. Redistributions in binary form must reproduce the above copyright\n"
"     *    notice, this list of conditions and the following disclaimer in\n"
"     *    the documentation and/or other materials provided with the\n"
"     *    distribution.\n"
"     *\n"
"     * 3. All advertising materials mentioning features or use of this\n"
"     *    software must display the following acknowledgment:\n"
"     *    \"This product includes software developed by the OpenSSL Project\n"
"     *    for use in the OpenSSL Toolkit. (http://www.openssl.org/)\n"
"     *\n"
"     * 4. The names \"OpenSSL Toolkit\" and \"OpenSSL Project\" must not be\n"
"     *    used to endorse or promote products derived from this software\n"
"     *    without prior written permission. For written permission, please\n"
"     *    contact <EMAIL>.\n"
"     *\n"
"     * 5. Products derived from this software may not be called \"OpenSSL\n"
"     *    nor may \"OpenSSL\" appear in their names without prior written\n"
"     *    permission of the OpenSSL Project.\n"
"     *\n"
"     * 6. Redistributions of any form whatsoever must retain the following\n"
"     *    acknowledgment:\n"
"     *    \"This product includes software developed by the OpenSSL Project\n"
"     *    for use in the OpenSSL Toolkit (http://www.openssl.org/)\n"
"     *\n"
"     * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY\n"
"     * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n"
"     * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\n"
"     * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR\n"
"     * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n"
"     * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n"
"     * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n"
"     * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)\n"
"     * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,\n"
"     * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n"
"     * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED\n"
"     * OF THE POSSIBILITY OF SUCH DAMAGE.\n"
"     * ====================================================================\n"
"     *\n"
"     * This product includes cryptographic software written by Eric Young\n"
"     * (<EMAIL>).  This product includes software written by Tim\n"
"     * Hudson (<EMAIL>).\n"
"     *\n"
"     *\n"
"\n"
"    Original SSLeay License\n"
"    -----------------------\n"
"\n"
"     * Copyright (C) 1995-1998 Eric Young (<EMAIL>)\n"
"     * All rights reserved.\n"
"     *\n"
"     * This package is an SSL implementation written\n"
"     * by Eric Young (<EMAIL>).\n"
"     * The implementation was written so as to conform with Netscapes SSL.\n"
"     *\n"
"     * This library is free for commercial and non-commercial use as long as\n"
"     * the following conditions are adhered to.  The following conditions\n"
"     * apply to all code found in this distribution, be it the RC4, RSA,\n"
"     * lhash, DES, etc., code; not just the SSL code.  The SSL documentation\n"
"     * included with this distribution is covered by the same copyright terms\n"
"     * except that the holder is Tim Hudson (<EMAIL>).\n"
"     *\n"
"     * Copyright remains Eric Young's, and as such any Copyright notices in\n"
"     * the code are not to be removed.\n"
"     * If this package is used in a product, Eric Young should be given\n"
"     * attribution as the author of the parts of the library used.\n"
"     * This can be in the form of a textual message at program startup or\n"
"     * in documentation (online or textual) provided with the package.\n"
"     *\n"
"     * Redistribution and use in source and binary forms, with or without\n"
"     * modification, are permitted provided that the following conditions\n"
"     * are met:\n"
"     * 1. Redistributions of source code must retain the copyright\n"
"     *    notice, this list of conditions and the following disclaimer.\n"
"     * 2. Redistributions in binary form must reproduce the above copyright\n"
"     *    notice, this list of conditions and the following disclaimer in the\n"
"     *    documentation and/or other materials provided with the distribution.\n"
"     * 3. All advertising materials mentioning features or use of this\n"
"     *    software must display the following acknowledgement:\n"
"     *    \"This product includes cryptographic software written by\n"
"     *     Eric Young (<EMAIL>)\"\n"
"     *    The word 'cryptographic' can be left out if the routines from the\n"
"     *    library being used are not cryptographic related  :-) .\n"
"     * 4. If you include any Windows specific code (or a derivative thereof)\n"
"     *    from the apps directory (application code) you must include an\n"
"     *    acknowledgement: \"This product includes software written by Tim\n"
"     *    Hudson (<EMAIL>)\"\n"
"     *\n"
"     * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND\n"
"     * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n"
"     * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\n"
"     * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS\n"
"     * BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n"
"     * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n"
"     * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR\n"
"     * BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n"
"     * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE\n"
"     * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN\n"
"     * IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"
"     *\n"
"     * The licence and distribution terms for any publicly available version\n"
"     * or derivative of this code cannot be changed.  i.e. this code cannot\n"
"     * simply be copied and put under another distribution licence\n"
"     * [including the GNU Public Licence.]\n"
"     *\n"
"\n"
"\n"
"    sol2 License\n"
"    -----------------------\n"
"\n"
"    Copyright (c) 2013-2018 Rapptz, ThePhD, and contributors\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining\n"
"    a copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to\n"
"    permit persons to whom the Software is furnished to do so, subject to\n"
"    the following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be\n"
"    included in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n"
"    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n"
"    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n"
"    LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n"
"    OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n"
"    WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    Lua License\n"
"    -----------------------\n"
"\n"
"    Copyright (c) 1994-2018 Lua.org, PUC-Rio.\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining\n"
"    a copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to\n"
"    permit persons to whom the Software is furnished to do so, subject to\n"
"    the following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be\n"
"    included in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n"
"    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n"
"    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n"
"    LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n"
"    OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n"
"    WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    cURL License\n"
"    -----------------------\n"
"\n"
"    This code uses a permissive license. Please use the following\n"
"    attribution in the documentation of the open source code.\n"
"\n"
"    Copyright (c) 1996 - 2018, Daniel Stenberg, <EMAIL>, and many\n"
"    contributors, see the THANKS file.\n"
"\n"
"    All rights reserved.\n"
"\n"
"    Permission to use, copy, modify, and distribute this software for any\n"
"    purpose with or without fee is hereby granted, provided that the above\n"
"    copyright notice and this permission notice appear in all copies.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n"
"    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF\n"
"    THIRD PARTY RIGHTS. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n"
"    BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n"
"    ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n"
"    CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n"
"    SOFTWARE.\n"
"\n"
"    Except as contained in this notice, the name of a copyright holder shall\n"
"    not be used in advertising or otherwise to promote the sale, use or\n"
"    other dealings in this Software without prior written authorization of\n"
"    the copyright holder.\n"
"\n"
"\n"
"    Lua-cURLv3 License\n"
"    -----------------------\n"
"\n"
"    Copyright (c) 2014-2018 Alexey Melnichuk\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining a\n"
"    copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to permit\n"
"    persons to whom the Software is furnished to do so, subject to the\n"
"    following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be included\n"
"    in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n"
"    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n"
"    NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n"
"    DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n"
"    OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n"
"    USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    LuaSQLite3 License\n"
"    -----------------------\n"
"\n"
"    Copyright (C) 2002-2016 Tiago Dionizio, Doug Currie\n"
"    All rights reserved.\n"
"    Author : Tiago Dionizio <<EMAIL>>\n"
"    Author : Doug Currie <<EMAIL>>\n"
"    Library : lsqlite3 - an SQLite 3 database binding for Lua 5\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining\n"
"    a copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to\n"
"    permit persons to whom the Software is furnished to do so, subject to\n"
"    the following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be\n"
"    included in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n"
"    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n"
"    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n"
"    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n"
"    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n"
"    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    lua-cjson License\n"
"    -----------------------\n"
"\n"
"    Copyright (c) 2010-2012 Mark Pulford <<EMAIL>>\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining\n"
"    a copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to\n"
"    permit persons to whom the Software is furnished to do so, subject to\n"
"    the following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be\n"
"    included in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n"
"    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n"
"    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n"
"    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n"
"    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n"
"    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    libc++ and libc++abi License\n"
"    -----------------------\n"
"\n"
"    These libraries are dual-licensed:\n"
"\n"
"    The University of Illinois/NCSA Open Source License (NCSA)\n"
"    Developed under the LLVM Project\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining a\n"
"    copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal with the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to\n"
"    permit persons to whom the Software is furnished to do so, subject to the\n"
"    following conditions:\n"
"\n"
"    Redistributions of source code must retain the above copyright notice,\n"
"    this list of conditions and the following disclaimers.\n"
"    Redistributions in binary form must reproduce the above copyright\n"
"    notice, this list of conditions and the following disclaimers in the\n"
"    documentation and/or other materials provided with the distribution.\n"
"    Neither the names of <Name of Development Group, Name of Institution>,\n"
"    nor the names of its contributors may be used to endorse or promote\n"
"    products derived from this Software without specific prior written\n"
"    permission.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n"
"    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n"
"    IN NO EVENT SHALL THE CONTRIBUTORS OR COPYRIGHT HOLDERS BE LIABLE FOR\n"
"    ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n"
"    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n"
"     SOFTWARE OR THE USE OR OTHER DEALINGS WITH THE SOFTWARE.\n"
"\n"
"    MIT\n"
"    Developed under the LLVM Project\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining a\n"
"    copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to permit\n"
"    persons to whom the Software is furnished to do so, subject to the\n"
"    following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be included\n"
"    in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n"
"    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n"
"    NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n"
"    DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n"
"    OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n"
"    USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"
"\n"
"    jemalloc\n"
"    -----------------------\n"
"\n"
"    Copyright (C) 2002-present Jason Evans <<EMAIL>>.\n"
"    All rights reserved.\n"
"    Copyright (C) 2007-2012 Mozilla Foundation.  All rights reserved.\n"
"    Copyright (C) 2009-present Facebook, Inc.  All rights reserved.\n"
"\n"
"    Redistribution and use in source and binary forms, with or without\n"
"    modification, are permitted provided that the following conditions are met:\n"
"    1. Redistributions of source code must retain the above copyright\n"
"       notice(s), this list of conditions and the following disclaimer.\n"
"    2. Redistributions in binary form must reproduce the above copyright\n"
"       notice(s), this list of conditions and the following disclaimer in the\n"
"       documentation and/or other materials provided with the distribution.\n"
"\n"
"    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDER(S) ``AS IS'' AND ANY\n"
"    EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n"
"    WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n"
"    DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY\n"
"    DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n"
"    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n"
"    SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n"
"    CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT\n"
"    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY\n"
"    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF\n"
"    SUCH DAMAGE.\n"
"\n"
"\n"
"    JSON for Modern C++\n"
"    -----------------------\n"
"\n"
"    Copyright (c) 2013-2018 Niels Lohmann\n"
"\n"
"    Permission is hereby granted, free of charge, to any person obtaining\n"
"    a copy of this software and associated documentation files (the\n"
"    \"Software\"), to deal in the Software without restriction, including\n"
"    without limitation the rights to use, copy, modify, merge, publish,\n"
"    distribute, sublicense, and/or sell copies of the Software, and to permit\n"
"    persons to whom the Software is furnished to do so, subject to the\n"
"    following conditions:\n"
"\n"
"    The above copyright notice and this permission notice shall be included\n"
"    in all copies or substantial portions of the Software.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n"
"    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n"
"    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n"
"    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n"
"    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT\n"
"    OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR\n"
"    THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n"
"\n"

"    optional-lite and any-lite\n"
"    Martin Moene\n"
"    -----------------------\n"
"\n"
"    Boost Software License - Version 1.0 - August 17th, 2003\n"
"\n"
"    Permission is hereby granted, free of charge, to any person or organization\n"
"    obtaining a copy of the software and accompanying documentation covered by\n"
"    this license (the \"Software\") to use, reproduce, display, distribute,\n"
"    execute, and transmit the Software, and to prepare derivative works of the\n"
"    Software, and to permit third-parties to whom the Software is furnished to\n"
"    do so, all subject to the following:\n"
"\n"
"    The copyright notices in the Software and this entire statement, including\n"
"    the above license grant, this restriction and the following disclaimer,\n"
"    must be included in all copies of the Software, in whole or in part, and\n"
"    all derivative works of the Software, unless such copies or derivative\n"
"    works are solely in the form of machine-executable object code generated by\n"
"    a source language processor.\n"
"\n"
"    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n"
"    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n"
"    FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT\n"
"    SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE\n"
"    FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,\n"
"    ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n"
"    DEALINGS IN THE SOFTWARE.\n"
"\n"
};

int
clientLegalHelp( Error *e )
{
	ClientUser cuser;
	e->Set( LegalHelp );
	cuser.Message( e );
	e->Clear();
	return 0;
}
