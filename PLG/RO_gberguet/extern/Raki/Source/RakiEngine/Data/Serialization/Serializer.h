#ifndef RAKI_SERIALIZER_H
#define RAKI_SERIALIZER_H





#include "RakiEngine/Data/Stream/SeekableStream.h"




namespace raki
{




class Serializer 
{

public:

    Serializer( SeekableStream * _seekableStream );
    
    virtual ~Serializer();

    inline void setIsReading();
    inline void setIsWriting();

    inline const bool isReading() const;
    inline const bool isWriting() const;

    virtual const u64 seek( const u64 _size, SeekableStream::SeekType _seekType );

    template< typename Type >
    inline Serializer & operator % ( Type & data )
    {
        u64 size = m_seekableStream->stream( &data, sizeof( data ) );

        if ( size != sizeof( Type ) )
            m_encounteredError = true;

        return *this;
    }


    inline void serialize( void * _data, u64 _size )
    {
        u64 size = m_seekableStream->stream( _data, _size );

        if ( size != _size )
            m_encounteredError = true;
    }

    inline const bool encounteredError() const;

    inline SeekableStream * getSeekableStream();

private:

     SeekableStream * m_seekableStream;
     bool m_encounteredError;
};


// inline implementation 

void Serializer::setIsReading()
{
    m_seekableStream->setIsReading();
}

void Serializer::setIsWriting()
{
    m_seekableStream->setIsWriting();
}

const bool Serializer::isReading() const
{
    return m_seekableStream->isReading();
}

const bool Serializer::isWriting() const
{
    return m_seekableStream->isWriting();
}

const bool Serializer::encounteredError() const
{
    return m_encounteredError;
}

SeekableStream * Serializer::getSeekableStream()
{
    return m_seekableStream;
}





} // namespace raki










#endif // RAKI_SERIALIZER_H

