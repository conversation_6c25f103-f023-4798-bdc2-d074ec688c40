#ifndef RAKI_RIFFCHUNKMANAGER_H
#define RAKI_RIFFCHUNKMANAGER_H



#include <list>

#include "RakiEngine/System/RakiSTLAllocator.h"

#include "RakiEngine/Data/Serialization/SerializedObject.h"




namespace raki
{


class RiffChunkManager
{
    RAKI_DECLARE_SINGLETON(RiffChunkManager);

public:

    RiffChunkManager();

    void addType( const char * _typeName, bool _isData );

    const bool isRegistered( const u32 _typeId );

    const bool isDataChunk( const u32 _typeId );

    void setDataChunkInfo( u32 _offset, u32 _size );

    const u32 getTypeIdFromName( const char * _typeName );

    const char * debugGetTypeNameFromId( const u32 _typeId );

    inline const bool shouldReadDataBlockForWaveData() const;

    inline void setShouldReadDataBlockForWaveData( const bool _read );

    inline const bool shouldSwapEndians() const;

    inline void setShouldSwapEndians( const bool _swap );

    inline const bool isWritingToBigEndians() const;

    inline void setIsWritingToBigEndians( const bool _writingToBigEndians );

private:

    struct ChunkType 
    {
        ChunkType( RiffChunkManager * _manager, const char * _typeName, bool _isData );
        u32 m_typeId;
        bool m_isData;
    };

    typedef std::list< ChunkType, StlAllocator< ChunkType, Memory::engine> > WaveChunkListType;

    WaveChunkListType m_typeList;

    u32 m_dataChunkOffset;
    u32 m_dataChunkSize;

    bool m_shouldReadDataBlockForWaveData;
    bool m_shouldSwapEndians;
    bool m_isWritingToBigEndians;

    char m_debugName[5];

};



// inlne implementation 


const bool RiffChunkManager::shouldReadDataBlockForWaveData() const 
{
    return m_shouldReadDataBlockForWaveData;
}


void RiffChunkManager::setShouldReadDataBlockForWaveData( const bool _read )
{
    m_shouldReadDataBlockForWaveData = _read;
}


const bool RiffChunkManager::shouldSwapEndians() const 
{
    return m_shouldSwapEndians;
}


void RiffChunkManager::setShouldSwapEndians( const bool _swap )
{
    m_shouldSwapEndians = _swap;
}


const bool RiffChunkManager::isWritingToBigEndians() const
{
    return m_isWritingToBigEndians;
}


void RiffChunkManager::setIsWritingToBigEndians( const bool _writingToBigEndians )
{
    m_isWritingToBigEndians = _writingToBigEndians;
}



} // namespace raki










#endif // RAKI_RIFFCHUNKMANAGER_H

