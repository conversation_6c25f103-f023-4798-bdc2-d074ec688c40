#include "Precompiled.h"





#include "RakiEngine/Data/WaveFile/RiffChunkManager.h"






RAKI_IMPLEMENT_SINGLETON(RiffChunkManager);




namespace raki
{



const u32 RiffChunkManager::getTypeIdFromName( const char * typeName )
{
    RAKI_ASSERT( typeName );
    RAKI_ASSERT( strlen( typeName ) == 4 );

#if defined ( RAKI_PLATFORM_XB360) || defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )

    u32 typeId = *(u32*) typeName;

#else // RAKI_PLATFORM_WIN32...

    u8 * pu = (u8*) typeName;

    u32 typeId = 0;

    typeId += ( ( (u32)( pu[0] ) ) << 0 );
    typeId += ( ( (u32)( pu[1] ) ) << 8 );
    typeId += ( ( (u32)( pu[2] ) ) << 16 );
    typeId += ( ( (u32)( pu[3] ) ) << 24 );

#endif // RAKI_PLATFORM_WIN32...

    return typeId;
}

const char * RiffChunkManager::debugGetTypeNameFromId( const u32 _typeId )
{
    const u8 * pu = (const u8*) &_typeId;

    m_debugName[0] = pu[0]; 
    m_debugName[1] = pu[1]; 
    m_debugName[2] = pu[2]; 
    m_debugName[3] = pu[3]; 
    m_debugName[4] = 0; 

    return m_debugName;
}





RiffChunkManager::RiffChunkManager()
    : m_dataChunkOffset( 0 )
    , m_dataChunkSize( 0 )
    , m_shouldReadDataBlockForWaveData( false )
    , m_shouldSwapEndians( false )
    , m_isWritingToBigEndians( false )
{
    m_debugName[0] = 0; 
    m_debugName[4] = 0; 

    addType( "data", true ); 
    addType( "fmt ", false );

#if defined RAKI_PLATFORM_WIN32

    // for wave markers and regions
    addType( "adtl", false);
    addType( "cue ", false);
    addType( "ltxt", false);
    addType( "labl", false);
    addType( "seek", false); 

#elif defined RAKI_PLATFORM_XB360

    addType( "seek", false); 

#elif defined RAKI_PLATFORM_PS3

    addType( "msf ", false );

#elif defined RAKI_PLATFORM_WII

    addType( "dspL", false );
    addType( "dspR", false );

    addType( "datL", true  );
    addType( "datR", true  );
    addType( "datS", true  );

#endif 
}


const bool RiffChunkManager::isDataChunk( const u32 _typeId )
{
    RAKI_ASSERT( m_typeList.size() ); // is init call missing? 

    WaveChunkListType::iterator it = m_typeList.begin();

    while ( it != m_typeList.end() )
    {
        if ( it->m_typeId == _typeId )
            return it->m_isData;

        ++it;
    }

    return false;
}


void RiffChunkManager::setDataChunkInfo( u32 _offset, u32 _size )
{
    m_dataChunkOffset = _offset;
    m_dataChunkSize = _size;
}



void RiffChunkManager::addType( const char * typeName, bool _isData )
{
    RAKI_ASSERT( strlen( typeName ) == 4 );

    // check that this type has not already been added
    RAKI_ASSERT( !isRegistered( getTypeIdFromName( typeName ) ) );

    ChunkType newType( this, typeName, _isData );

    m_typeList.push_back( newType);
}


const bool RiffChunkManager::isRegistered( const u32 typeId )
{
    // check that this type has not already been added
    WaveChunkListType::iterator it = m_typeList.begin();

    while ( it != m_typeList.end() )
    {
        if ( it->m_typeId == typeId )
            return true;

        ++it;
    }

    return false;
}




RiffChunkManager::ChunkType::ChunkType( RiffChunkManager * _manager, const char * _typeName, bool _isData )
{
    RAKI_ASSERT( _typeName );
    RAKI_ASSERT( strlen( _typeName ) == 4 );

    m_typeId = _manager->getTypeIdFromName( _typeName );
    m_isData = _isData;
}




} // namespace raki









