#ifndef RAKI_F0RMATNATIVE_NINTENDO_H
#define RAKI_F0RMATNATIVE_NINTENDO_H

#pragma once

#include "RakiEngine/System/RakiTypes.h"

namespace raki
{
    class MemoryBlock;

    class FormatNative
    {
    public:
        FormatNative();
        virtual ~FormatNative();

        void setData( WAVEFORMATEX * _format, const u64 _waveFormatSize, MemoryBlock * _data = NULL );

        inline const WAVEFORMATEX* getWaveFormatData() const { return m_format; }
        inline const MemoryBlock* getMemoryBlock() const { return m_data; }

    private:
        WAVEFORMATEX * m_format;
        u64 m_waveFormatSize;
        MemoryBlock * m_data;
    };
} // namespace raki

#endif // RAKI_F0RMATNATIVE_NINTENDO_H
