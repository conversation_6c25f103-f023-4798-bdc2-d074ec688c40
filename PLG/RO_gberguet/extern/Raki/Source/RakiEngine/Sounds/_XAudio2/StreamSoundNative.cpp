#include "Precompiled.h"





#include "RakiEngine/Sounds/_XAudio2/StreamSoundNative.h"





#include "RakiEngine/Initializer/EngineInitializer.h"
#include "RakiEngine/System/AudioCallbackManager/AudioCallbackManager.h"
#include "RakiEngine/System/Atomic/Atomic.h"



//#define LOG_STREAMSOUNDNATIVE




namespace raki
{


StreamSoundNative::StreamSoundNative()
    : m_sourceVoice( NULL )
    , m_doubleBufferMaxSize( 0 )
    , m_nextBufferToSubmit( 0 )
    , m_currentlyPlayingBufferInfo( NULL )
    , m_sourceIndex( 0 )
    , m_beginningOfBufferSamplePos( 0 )
    , m_samplePos( 0 )
    , m_updateLastSamplePosition( 0 )
    , m_nbChannels( 0 )
    , m_startWaitingForFirstData( false )
{
    for ( int i = 0 ; i < STREAMSOUNDNATIVE_NBBUFFERS ; ++i)
    {
        m_bufferArray[ i ].m_playBegin = 0;
        m_bufferArray[ i ].m_playLength = 0;
    }
}


StreamSoundNative::~StreamSoundNative()
{
    destroy();

    freeBuffers();
}


bool StreamSoundNative::allocateBuffers( u64 _halfBufferSize )
{
    RAKI_ASSERT( ( ( _halfBufferSize ) % STREAMSOUNDNATIVE_SIZEALIGNMENT ) == 0 );

    for ( int i = 0 ; i < STREAMSOUNDNATIVE_NBBUFFERS ; ++i)
    {
        MemoryBlock::MemoryType type = MemoryBlock::standard;

#if defined ( RAKI_PLATFORM_XB360 )
        type = MemoryBlock::xma2_aligned2048;
#endif // RAKI_PLATFORM_XB360

        m_bufferArray[ i ].allocateBuffer( 2 * _halfBufferSize, type, Memory::streamBuffers );

        if ( !m_bufferArray[ i ].getBuffer() )
            return false;
    }

    m_doubleBufferMaxSize = 2 * _halfBufferSize;

    return true;
}


void StreamSoundNative::freeBuffers()
{
    for ( int i = 0 ; i < STREAMSOUNDNATIVE_NBBUFFERS ; ++i)
        m_bufferArray[ i ].freeBuffer();

    m_doubleBufferMaxSize = 0;
}


void StreamSoundNative::destroy()
{
    if ( m_sourceVoice )
        m_sourceVoice->DestroyVoice();

    m_sourceVoice = NULL;
}


void StreamSoundNative::start()
{
    if ( m_sourceVoice )
    {
        XAUDIO2_VOICE_STATE state = { 0 };

        m_sourceVoice->Discontinuity();

#if defined ( RAKI_PLATFORM_XB360 )
        m_sourceVoice->GetState( &state, 0 );
#else // RAKI_PLATFORM_XB360
        m_sourceVoice->GetState( &state );
#endif // RAKI_PLATFORM_XB360

        RAKI_ASSERT( !state.SamplesPlayed );

        m_samplePos = 0;

        m_updateLastSamplePosition = 0;

        m_sourceVoice->Start( 0 );

        if ( !getNbSubmittedBuffers() )
            m_startWaitingForFirstData = true;

        RAKI_ASSERT( isPlaying() );

//        m_isPlaying = true;
    }
}


void StreamSoundNative::stop()
{
    m_startWaitingForFirstData = false;

    if ( m_sourceVoice )
    {
        m_sourceVoice->Stop( 0 );

        m_sourceVoice->FlushSourceBuffers();
    }
    m_beginningOfBufferSamplePos = 0;
}


void StreamSoundNative::pause()
{
    if ( m_sourceVoice )
        m_sourceVoice->Stop( 0 );
}


void StreamSoundNative::resume()
{
    if ( m_sourceVoice )
        m_sourceVoice->Start( 0 );
}


void StreamSoundNative::requestEndAfterSubmittedBuffers()
{
    // nothing to do on submitted XAudio2 buffers...
}


void StreamSoundNative::resetRequestEndAfterSubmittedBuffers()
{
    // nothing to do on submitted XAudio2 buffers...
}


void StreamSoundNative::OnVoiceProcessingPassStart( UINT32 /*BytesRequired*/ )
{
    XAUDIO2_VOICE_STATE state = { 0 };

    if ( !m_sourceVoice )
        return;

    AudioCallbackObject audioCallbackObject;

    if ( !audioCallbackObject.isActive() )
        return;

#if defined ( RAKI_PLATFORM_XB360 )
    m_sourceVoice->GetState( &state, 0 );
#else // RAKI_PLATFORM_XB360
    m_sourceVoice->GetState( &state );
#endif // RAKI_PLATFORM_XB360

    if ( state.pCurrentBufferContext )
    {

        StreamBufferInfo::Info * info = ( StreamBufferInfo::Info * )state.pCurrentBufferContext;

        if ( m_currentlyPlayingBufferInfo != ( StreamBufferInfo::Info * )state.pCurrentBufferContext )
        {
            m_beginningOfBufferSamplePos = state.SamplesPlayed;
        }

        // XAudio2 bug on PC with PCM streamed data: state.BuffersQueued is not updated just after SubmitSourceBuffer 
        //info->setNbBuffersQueued( m_nbBuffersQueued ) ;//state.BuffersQueued 

        if ( info->isActiveBuffer() )
        {
            m_samplePos = info->getSourceSamplePositionBeforeSubmit() + ( state.SamplesPlayed - m_beginningOfBufferSamplePos );

#ifdef LOG_STREAMSOUNDNATIVE
            RAKI_OUTPUT("0x%x StreamSoundNative::OnVoiceProcessingPassStart state.SamplesPlayed 0x%llx calculation 0x%llx (before submit 0x%llx beginning of buffer 0x%llx buffer context 0x%x)", 
                this, state.SamplesPlayed, m_samplePos, info->getSourceSamplePositionBeforeSubmit(), info->getMeasuredSamplesAtBeginningOfBuffer(), state.pCurrentBufferContext );
#endif // LOG_STREAMSOUNDNATIVE

            info->update( m_samplePos );
        }
    }

    Atomic::setWithMemBarrier( m_currentlyPlayingBufferInfo, ( StreamBufferInfo::Info * )state.pCurrentBufferContext );
}


void StreamSoundNative::OnVoiceProcessingPassEnd()
{
//    RAKI_OUTPUT("StreamSoundNative::OnVoiceProcessingPassEnd 0x%x", this );
}


void StreamSoundNative::OnStreamEnd()
{
//    RAKI_OUTPUT("StreamSoundNative::OnStreamEnd 0x%x", this );
}


void StreamSoundNative::OnBufferStart(void * _bufferContext )
{
    XAUDIO2_VOICE_STATE state = { 0 };

    if ( !m_sourceVoice )
        return;

#if defined ( RAKI_PLATFORM_XB360 )
    m_sourceVoice->GetState( &state, 0 );
#else // RAKI_PLATFORM_XB360
    m_sourceVoice->GetState( &state );
#endif // RAKI_PLATFORM_XB360

    if ( _bufferContext )
    {
        StreamBufferInfo::Info * info = ( StreamBufferInfo::Info * )_bufferContext;

        if ( info->getSourceSamplePositionBeforeSubmit() )
            info->setActiveBuffer( state.SamplesPlayed );
        else
            info->setActiveBuffer( 0 );

        //RAKI_OUTPUT("0x%x StreamSoundNative::OnBufferStart on info 0x%x - SamplesPlayed %d", this, info, state.SamplesPlayed );
    }
}


void StreamSoundNative::OnBufferEnd(void * /*pBufferContext*/ )
{
#ifdef LOG_STREAMSOUNDNATIVE
    XAUDIO2_VOICE_STATE state = { 0 };

    m_sourceVoice->GetState( &state/*, 0*/ );

//    RAKI_OUTPUT("StreamSoundNative::OnBufferEnd 0x%x - SamplesPlayed %d - BuffersQueued %d - m_nbBuffersQueued %d", this, state.SamplesPlayed, state.BuffersQueued, m_nbBuffersQueued );
#endif // LOG_STREAMSOUNDNATIVE
}


void StreamSoundNative::OnLoopEnd(void * /*pBufferContext*/ )
{
#ifdef LOG_STREAMSOUNDNATIVE
    RAKI_OUTPUT("StreamSoundNative::OnLoopEnd 0x%x", this );
#endif // LOG_STREAMSOUNDNATIVE
}


void StreamSoundNative::OnVoiceError(void * /*pBufferContext*/, HRESULT /*Error*/ )
{
    RAKI_OUTPUT("StreamSoundNative::OnVoiceError 0x%x", this );
}


bool StreamSoundNative::isPlaying() const
{
    if ( getIsPendingSyncStart() )
        return true;

    if ( m_startWaitingForFirstData )
        return true;

    return getNbSubmittedBuffers() ? true : false;
}


u32 StreamSoundNative::getNbSubmittedBuffers() const 
{
    if ( m_sourceVoice )
    {
        XAUDIO2_VOICE_STATE state = { 0 };

        m_sourceVoice->GetState( &state );

        return state.BuffersQueued;
    }

    return 0;
}


void StreamSoundNative::internalSetDecibelVolume( f32 _decibelVolume )
{
    RAKI_ASSERT( _decibelVolume <= 0.f );

    f32 linearVolume;

    if ( _decibelVolume < RAKI_ROUTING_MIN_VOLUME_DECIBELS )
        linearVolume = 0.f;
    else 
        linearVolume = Calcs::getLinearVolumeFromDecibelVolume( _decibelVolume );

    if ( m_sourceVoice )
        m_sourceVoice->SetVolume( linearVolume );
}


void StreamSoundNative::setPitch( const f32 /*_pitch*/ )
{
    RAKI_ASSERT_FALSE; // don't use if not implemented
}

u64 StreamSoundNative::getSamplePosition()
{
    if ( isPlaying() && m_sourceVoice )
    {
        return m_samplePos;
    }

    return 0;
}

void StreamSoundNative::setPan( const f32 _pan )
{
    // pan is only for mono 
    if ( m_nbChannels != 1 )
        return;

    RAKI_ASSERT( _pan >= -1.f );
    RAKI_ASSERT( _pan <= 1.f );

    const f32 normalizedPan = ( 1.f + _pan ) / 2.f;

    const f32 left = sqrtf( 1.f - normalizedPan );
    const f32 right = sqrtf( normalizedPan );

    float outputMatrix[ 8 ];
    for ( int i = 0; i < 8; ++i ) 
        outputMatrix[i] = 0.f;

    if ( m_sourceVoice )
    {
        const u32 deviceChannelMask = EngineInitializer::singleton().getDeviceChannelMask();
        const u32 deviceNbChannels = EngineInitializer::singleton().getDeviceNbChannels();

        switch ( deviceChannelMask )
        {
        case SPEAKER_MONO:
            outputMatrix[0] = 1.0;
            break;
        case SPEAKER_STEREO:
        case SPEAKER_2POINT1:
        case SPEAKER_SURROUND:
            outputMatrix[0] = left;
            outputMatrix[1] = right;
            break;
        case SPEAKER_QUAD:
            outputMatrix[0] = outputMatrix[2] = left;
            outputMatrix[1] = outputMatrix[3] = right;
            break;
        case SPEAKER_4POINT1:
            outputMatrix[ 0 ] = outputMatrix[ 3 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 4 ] = right;
            break;
        case SPEAKER_5POINT1:
        case SPEAKER_7POINT1:
        case SPEAKER_5POINT1_SURROUND:
            outputMatrix[ 0 ] = outputMatrix[ 4 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 5 ] = right;
            break;
        case SPEAKER_7POINT1_SURROUND:
            outputMatrix[ 0 ] = outputMatrix[ 4 ] = outputMatrix[ 6 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 5 ] = outputMatrix[ 7 ] = right;
            break;
        }

        m_sourceVoice->SetOutputMatrix( NULL, 1, deviceNbChannels, outputMatrix );       
    }
}






// StreamSoundNative::Buffer implementation 

StreamSoundNative::Buffer::Buffer()
{
}

StreamSoundNative::Buffer::~Buffer()
{
}



} // namespace raki

