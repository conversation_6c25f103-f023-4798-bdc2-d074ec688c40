#include "Precompiled.h"



#include "RakiEngine/Sounds/_XAudio2/BufferSoundNative.h"



#include "RakiEngine/Data/MemoryBlock/MemoryBlock.h"
#include "RakiEngine/Initializer/EngineInitializer.h"
#include "RakiEngine/Sounds/Format.h"

#if defined ( RAKI_TARGET_XB360 )
#include "RakiEngine/Specific/_XB360/SpecificXB360.h"
#endif // defined ( RAKI_TARGET_XB360 )


//#define LOG_BUFFERSOUNDNATIVE


namespace raki
{


BufferSoundNative::BufferSoundNative()
    : m_sourceVoice( NULL )
    , m_bufferEnded( false )
    , m_looping( false )
    , m_loopingAndStarted( false )
    , m_nbChannels( 0 )
    , m_xma2( false )
{
}


BufferSoundNative::~BufferSoundNative()
{
    destroyVoice();
}


bool BufferSoundNative::createVoice( const Format * _format )
{
    RAKI_ASSERT( _format );
    RAKI_ASSERT( _format->getWaveFormatData() );
    RAKI_ASSERT( _format->getWaveFormatSize() );

    IXAudio2 * xaudio2 = EngineInitializer::singleton().getXAudio2();

    // create voice 

    WAVEFORMATEX * originalFormat = _format->getWaveFormatData();

    u16 formatTag = originalFormat->wFormatTag;
    m_nbChannels = originalFormat->nChannels;

    switch ( formatTag )
    {

#if defined ( RAKI_PLATFORM_XB360 )

    case WAVE_FORMAT_XMA2:
        {
            m_xma2 = true;

            XMA2WAVEFORMATEX format;

            RAKI_ASSERT( _format->getWaveFormatSize() == sizeof( XMA2WAVEFORMATEX) );
            Memory::memcpy( &format, _format->getWaveFormatData(), sizeof( XMA2WAVEFORMATEX) );

            RAKI_ASSERT( format.wfx.wBitsPerSample < 256000 );
            RAKI_ASSERT( format.wfx.nChannels <= 2 );

            UINT32 flags =  0;
            if ( _format->isMusic() )
                flags |= XAUDIO2_VOICE_MUSIC;

            HRESULT hr = xaudio2->CreateSourceVoice( &m_sourceVoice, (WAVEFORMATEX*)&format, flags, XAUDIO2_DEFAULT_FREQ_RATIO, this  ); 

            if ( hr !=  S_OK )
            {
                RAKI_ASSERT( !m_sourceVoice );
                return false;
            }

        }
        break;

#endif // ( RAKI_PLATFORM_XB360 )

    case WAVE_FORMAT_PCM:
    case WAVE_FORMAT_ADPCM:
        {
            m_xma2 = false;

            WAVEFORMATEX waveFormat = { 0 };

            WAVEFORMATEX * createVoiceFormatex = NULL;

            if ( _format->getWaveFormatSize() < sizeof( WAVEFORMATEX) )
            {
                // thanks Bill...
                Memory::memcpy( &waveFormat, _format->getWaveFormatData(), _format->getWaveFormatSize() );
                waveFormat.cbSize = 0;

                createVoiceFormatex = &waveFormat;
            }
            else
            {
                createVoiceFormatex = _format->getWaveFormatData();
            }


            RAKI_ASSERT( createVoiceFormatex->wBitsPerSample < 256000 );
            RAKI_ASSERT( createVoiceFormatex->nChannels <= 2 );

            HRESULT hr = xaudio2->CreateSourceVoice( &m_sourceVoice, createVoiceFormatex, 0, XAUDIO2_DEFAULT_FREQ_RATIO, this ); 

            if ( hr !=  S_OK )
            {
                RAKI_ASSERT( !m_sourceVoice );
                return false;
            }
        }
        break;

    default:
        RAKI_ASSERT_FALSE;
    }

#if defined ( RAKI_PLATFORM_XB360 )

    if ( _format->getMemoryBlock() )
        return submitData( _format->getMemoryBlock(), _format->getLoopSampleSize(), _format->isLooping() );

#else // ( RAKI_PLATFORM_XB360 )

    if ( _format->getMemoryBlock() )
        return submitData( _format->getMemoryBlock(), 0, _format->isLooping() );

#endif // ( RAKI_PLATFORM_XB360 )

    return false;
}

void BufferSoundNative::destroyVoice()
{
    if ( m_sourceVoice )
        m_sourceVoice->DestroyVoice();

    m_sourceVoice = NULL;
}


bool BufferSoundNative::submitData( const MemoryBlock * _data, u64 _loopSampleSize, const bool _isLooping )
{
#ifdef LOG_BUFFERSOUNDNATIVE
    RAKI_OUTPUT("0x%x BufferSoundNative::submitData block buffer 0x%x %s", this, _data ? _data->getBuffer() : NULL, _isLooping ? "looping" : "" );
#endif // LOG_BUFFERSOUNDNATIVE

    RAKI_ASSERT( m_sourceVoice );
    RAKI_ASSERT( _data->getBuffer() );
    RAKI_ASSERT( _data->getSize() );

    if ( !m_sourceVoice )
        return false;

#ifdef RAKI_TARGET_DEBUG
    XAUDIO2_VOICE_STATE state = { 0 };
    m_sourceVoice->GetState( &state );
    RAKI_ASSERT( state.BuffersQueued == 0 );
#endif // RAKI_TARGET_DEBUG

    XAUDIO2_BUFFER buffer = { 0 };
    buffer.pAudioData = (BYTE*)_data->getBuffer();
    buffer.Flags = XAUDIO2_END_OF_STREAM;  // tell the source voice not to expect any data after this buffer
    buffer.AudioBytes = (UINT32)_data->getSize();
    buffer.LoopCount = _isLooping ? XAUDIO2_LOOP_INFINITE : 0;

    if ( _isLooping && m_xma2 )
    {
        buffer.LoopLength = (u32)_loopSampleSize;

        // From Tom Matthews, Microsoft:
        // One common pitfall: Note that the LoopBegin should be 384 samples to account for encoder ramp-up time 
        // (the first three subframes are used only on the first playthrough of the content). 
        // The encoder should set this value for you when using /LoopWholeFile, but it’s an idea to verify that XAudio2 is getting passed this value.

        buffer.LoopBegin = 384;
    }


    RAKI_ASSERT( !m_loopingAndStarted );
    m_looping = _isLooping;

    m_bufferEnded = 0;

    HRESULT hr = m_sourceVoice->SubmitSourceBuffer( &buffer ); 

    if ( hr != S_OK )
        return false;

    return true;
}



void BufferSoundNative::start()
{
#ifdef LOG_BUFFERSOUNDNATIVE
    RAKI_OUTPUT("0x%x BufferSoundNative::start m_sourceVoice 0x%x", this, m_sourceVoice );
#endif // LOG_BUFFERSOUNDNATIVE

    if ( m_sourceVoice )
    {
        m_sourceVoice->Start( 0 );

        RAKI_ASSERT( isPlaying() );

        if ( m_looping )
            m_loopingAndStarted = true;
    }
}

void BufferSoundNative::stop()
{
#ifdef LOG_BUFFERSOUNDNATIVE
    RAKI_OUTPUT("0x%x BufferSoundNative::stop m_sourceVoice 0x%x", this, m_sourceVoice );
#endif // LOG_BUFFERSOUNDNATIVE

    m_bufferEnded = 1;

    if ( m_sourceVoice )
        m_sourceVoice->Stop( XAUDIO2_PLAY_TAILS, XAUDIO2_COMMIT_NOW );

    m_loopingAndStarted = false;
}


void BufferSoundNative::pause()
{
    if ( m_sourceVoice )
        m_sourceVoice->Stop( XAUDIO2_PLAY_TAILS, XAUDIO2_COMMIT_NOW );
}


void BufferSoundNative::resume()
{
    start();
}


bool BufferSoundNative::isPlaying() const
{
    if ( getIsPendingSyncStart() )
        return true;

    if ( m_loopingAndStarted )
        return true;

    if ( m_sourceVoice )
    {
        XAUDIO2_VOICE_STATE state = { 0 };

        m_sourceVoice->GetState( &state );
    
        return state.BuffersQueued ? !m_bufferEnded : false;
    }

    return false;
}


void BufferSoundNative::internalSetDecibelVolume( f32 _decibelVolume )
{
    RAKI_ASSERT( _decibelVolume <= 0.f );

    f32 linearVolume;

    if ( _decibelVolume < RAKI_ROUTING_MIN_VOLUME_DECIBELS )
        linearVolume = 0.f;
    else 
        linearVolume = Calcs::getLinearVolumeFromDecibelVolume( _decibelVolume );

    if ( m_sourceVoice )
        m_sourceVoice->SetVolume( linearVolume );
}


void BufferSoundNative::setPitch( const f32 _pitch )
{
    if ( m_sourceVoice )
        m_sourceVoice->SetFrequencyRatio( _pitch );
}


void BufferSoundNative::setPan( const f32 _pan )
{
    // pan is only for mono 
    if ( m_nbChannels != 1 )
        return;

    RAKI_ASSERT( _pan >= -1.f );
    RAKI_ASSERT( _pan <= 1.f );

    const f32 normalizedPan = ( 1.f + _pan ) / 2.f;

    const f32 left = sqrtf( 1.f - normalizedPan );
    const f32 right = sqrtf( normalizedPan );

    float outputMatrix[ 8 ];
    for ( int i = 0; i < 8; ++i ) 
        outputMatrix[i] = 0.f;

    if ( m_sourceVoice )
    {
        const u32 deviceChannelMask = EngineInitializer::singleton().getDeviceChannelMask();
        const u32 deviceNbChannels = EngineInitializer::singleton().getDeviceNbChannels();

        switch ( deviceChannelMask )
        {
        case SPEAKER_MONO:
            outputMatrix[0] = 1.0;
            break;
        case SPEAKER_STEREO:
        case SPEAKER_2POINT1:
        case SPEAKER_SURROUND:
            outputMatrix[0] = left;
            outputMatrix[1] = right;
            break;
        case SPEAKER_QUAD:
            outputMatrix[0] = outputMatrix[2] = left;
            outputMatrix[1] = outputMatrix[3] = right;
            break;
        case SPEAKER_4POINT1:
            outputMatrix[ 0 ] = outputMatrix[ 3 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 4 ] = right;
            break;
        case SPEAKER_5POINT1:
        case SPEAKER_7POINT1:
        case SPEAKER_5POINT1_SURROUND:
            outputMatrix[ 0 ] = outputMatrix[ 4 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 5 ] = right;
            break;
        case SPEAKER_7POINT1_SURROUND:
            outputMatrix[ 0 ] = outputMatrix[ 4 ] = outputMatrix[ 6 ] = left;
            outputMatrix[ 1 ] = outputMatrix[ 5 ] = outputMatrix[ 7 ] = right;
            break;
        }

        m_sourceVoice->SetOutputMatrix( NULL, 1, deviceNbChannels, outputMatrix );       
    }
}


// IXAudio2VoiceCallback methods 


void BufferSoundNative::OnVoiceProcessingPassStart( UINT32 /*BytesRequired*/ )
{
}


void BufferSoundNative::OnVoiceProcessingPassEnd()
{
}


void BufferSoundNative::OnStreamEnd()
{
    m_bufferEnded = 1;
}


void BufferSoundNative::OnBufferStart(void * /*_bufferContext*/ )
{
}


void BufferSoundNative::OnBufferEnd(void * /*pBufferContext*/ )
{
}


void BufferSoundNative::OnLoopEnd(void * /*pBufferContext*/ )
{
}


void BufferSoundNative::OnVoiceError(void * /*pBufferContext*/, HRESULT /*Error*/ )
{
}



} // namespace raki

