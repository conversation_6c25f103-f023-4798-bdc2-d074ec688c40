#ifndef RAKI_STREAMSOUNDNATIVE_H
#define RAKI_STREAMSOUNDNATIVE_H





#include "RakiEngine/Data/MemoryBlock/MemoryBlock.h"
#include "RakiEngine/Sounds/SoundStreamDataBuffer.h"
#include "RakiEngine/Sounds/StreamBufferInfo.h"
#include "RakiEngine/Sounds/VoiceBase.h"





#define STREAMSOUNDNATIVE_NBBUFFERS                 2
#define STREAMSOUNDNATIVE_SIZEALIGNMENT             128



namespace raki
{



class StreamSoundNative
    : public IXAudio2VoiceCallback
    , public VoiceBase
{
public:

    StreamSoundNative();

    virtual ~StreamSoundNative();

    // careful max size is not used, only half buffer size is normally used (this is to be able more than half buf size if we're getting close to the end of the stream)
    virtual bool allocateBuffers( u64 _halfBufferSize );

    virtual void freeBuffers();

    virtual void destroy();

    virtual void start();

    virtual void stop();

    virtual void pause();

    virtual void resume();

    virtual bool isPlaying() const;

    virtual void internalSetDecibelVolume( f32 _decibelVolume );

    virtual void setPitch( f32 _pitch );

    virtual void setPan( f32 _pan );

    virtual void requestEndAfterSubmittedBuffers();

    virtual void resetRequestEndAfterSubmittedBuffers();

    u32 getNbSubmittedBuffers() const;

    inline StreamBufferInfo * getStreamBufferInfo();

    u64 getSamplePosition();

    inline bool hasValidLowLevelVoice() const;

protected:

    // IXAudio2VoiceCallback methods 
    STDMETHOD_(void, OnVoiceProcessingPassStart) (THIS_ UINT32 BytesRequired);
    STDMETHOD_(void, OnVoiceProcessingPassEnd) (THIS);
    STDMETHOD_(void, OnStreamEnd) (THIS);
    STDMETHOD_(void, OnBufferStart) (THIS_ void* pBufferContext);
    STDMETHOD_(void, OnBufferEnd) (THIS_ void* pBufferContext);
    STDMETHOD_(void, OnLoopEnd) (THIS_ void* pBufferContext);
    STDMETHOD_(void, OnVoiceError) (THIS_ void* pBufferContext, HRESULT Error);

    IXAudio2SourceVoice * m_sourceVoice;

    StreamBufferInfo m_streamBufferInfo;

    volatile StreamBufferInfo::Info * m_currentlyPlayingBufferInfo;

    u64 m_sourceIndex;

    class Buffer : public MemoryBlock
    {
    public:
        Buffer();
        virtual ~Buffer();

        u64 m_sizeToSubmit;
        u64 m_playBegin;
        u64 m_playLength;
    };

    Buffer m_bufferArray[STREAMSOUNDNATIVE_NBBUFFERS];

    // careful max size is not used, only half buffer size is normally used (this is to be able more than half buf size if we're getting close to the end of the stream)
    u64 m_doubleBufferMaxSize; 

    u32 m_nbChannels;

    volatile u64 m_samplePos;
    u64 m_beginningOfBufferSamplePos;
    u64 m_updateLastSamplePosition;

    u32 m_nextBufferToSubmit;

    bool m_startWaitingForFirstData;

};


StreamBufferInfo * StreamSoundNative::getStreamBufferInfo()
{
    return &m_streamBufferInfo;
}


bool StreamSoundNative::hasValidLowLevelVoice() const
{
    return m_sourceVoice ? true : false;
}









} // namespace raki




#endif // RAKI_STREAMSOUNDNATIVE_H


