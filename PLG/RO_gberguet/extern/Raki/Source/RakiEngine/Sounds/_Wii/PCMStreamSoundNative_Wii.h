#ifndef RAKI_PCMSTREAMSOUNDNATIVE_WII_H
#define RAKI_PCMSTREAMSOUNDNATIVE_WII_H




/*
#include "RakiEngine/Sounds/_WII/StreamSoundNative_WII.h"




namespace raki
{


class Format;


class PCMStreamSoundNative
    : public StreamSoundNative
{
public:

    PCMStreamSoundNative();

    virtual ~PCMStreamSoundNative();

    virtual bool createVoice( const Format * _format );

    virtual void prepare( SoundStreamDataProvider * _dataProvider );

    virtual void update();

    virtual bool isStopping(); 

    virtual bool isStopped(); 

    virtual void stop();

    // debugging...
    //virtual void start();
    //virtual void pause();
    //virtual void resume();
    //virtual void setLinearVolume( const f32 _linearVolume );

private:

    // AudioCallbackManager::PreCallback
    virtual void preCallback();
    
    void setBufferAddresses( const int _bufferIndex, const u32 _sampleSize );

    void setEndAddressOnBuffer( const int _bufferIndex, const u32 _sampleSize, const bool _isLastBuffer );
    
    void setLoopAddressOnBuffer( const int _bufferIndex );
    
    void copyInterlacedBufferToChannelBuffers( const int _bufferIndex );

    bool createWithData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit );

    virtual void VoiceIsReacquiredByAX();
    
    void needsMoreData( const int _bufferIndex );

    bool shouldSubmitNextBuffer();

    bool submitData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit, const bool _writeAddresses );

    void setEmptySecondRead(); 

    bool pullDataToBuffer( const int _bufferIndex );

    SoundStreamDataProvider * m_dataProvider;

    u64 m_sourceIndex;
    
    u32 m_previousBufferSampleSize;

    bool m_isStopping;

    bool m_needsMoreDataArray[ 2 ];
};





} // namespace raki

*/


#endif // RAKI_PCMSTREAMSOUNDNATIVE_WII_H


