#ifndef RAKI_F0RMATNATIVE_PS3_H
#define RAKI_F0RMATNATIVE_PS3_H


namespace raki
{

class MemoryBlock;


class FormatNative
{
public:

    FormatNative();

    virtual ~FormatNative();

    void setData( WAVEFORMATEX * _format, const u64 _waveFormatSize, MemoryBlock * _data = NULL );

    inline const WAVEFORMATEX * getWaveFormatData() const;
    inline const MemoryBlock * getMemoryBlock() const;

private:
    WAVEFORMATEX * m_format;
    u64 m_waveFormatSize;
    MemoryBlock * m_data;
};



// inline implementation 


const WAVEFORMATEX * FormatNative::getWaveFormatData() const 
{
    return m_format;
}


const MemoryBlock * FormatNative::getMemoryBlock() const 
{
    return m_data;
}





} // namespace raki




#endif // RAKI_F0RMATNATIVE_PS3_H


