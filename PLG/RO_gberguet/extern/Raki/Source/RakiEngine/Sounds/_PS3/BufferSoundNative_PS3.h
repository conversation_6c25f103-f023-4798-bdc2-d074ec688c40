#ifndef RAKI_BUFFERSOUNDNATIVE_PS3_H
#define RAKI_BUFFERSOUNDNATIVE_PS3_H




#include "RakiEngine/Sounds/_PS3/SoundNative_PS3.h"




namespace raki
{


class Format;
class MemoryBlock;


class BufferSoundNative
    : public SoundNative
{
public:

    BufferSoundNative();

    virtual ~BufferSoundNative();

    virtual bool createVoice( const Format * _format );

    virtual void destroyVoice();

    virtual const bool isPlaying() const;

};




} // namespace raki




#endif // RAKI_BUFFERSOUNDNATIVE_PS3_H


