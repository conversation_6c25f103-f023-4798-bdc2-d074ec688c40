#ifndef RAKI_STREAMBUFFERINFO_H
#define RAKI_STREAMBUFFERINFO_H




#include "RakiEngine/Sounds/Sequencer/Sequencer.h"






#include "RakiEngine/Sounds/SoundStreamDataBuffer.h"




#define STREAMBUFFERINFO_NB_INFOSTRUCT  4



// This is just a circular buffer containing info for submitted buffers 
// since we have STREAMSOUNDNATIVE_NBBUFFERS low level buffers and and submits are done asynchronously, 
// we need more than STREAMSOUNDNATIVE_NBBUFFERS buffer infos to be sure to be able to read the data when we want it 


namespace raki
{


class SoundStreamDataProvider;


class StreamBufferInfo
{
public:




    class StreamUpdateClient
    {
    public:
        virtual ~StreamUpdateClient();

        virtual void updateStreamPosition( const void * _clientData, const u64 _samplePosition ) = 0;
    };




    class Info
    {
        friend class StreamBufferInfo;

    public:
        Info();

        void update( u64 samplePosition );

#if defined ( RAKI_USING_XAUDIO2 )
        void setInfo( const u32 _bufferIndex, const void * _clientData, 
            const u64 _sourceSamplePositionBeforeSubmit, const u64 _sourceIndex, const bool _isLastBuffer );
#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )
        void setInfo( const void * _dataBuffer, const void * _clientData, 
            const u64 _sourceSamplePositionBeforeSubmit, const u32 _bufferSampleSize, const u64 _sourceIndex, const bool _isLastBuffer );
#endif 

        inline const u64 getSourceSamplePositionBeforeSubmit() const
        {
            return m_sourceSamplePositionBeforeSubmit;
        }
        
        inline void setActiveBuffer( const u64 _measuredSamplesAtBeginningOfBuffer )
        {
            m_streamBufferInfo->setActiveBuffer( this, _measuredSamplesAtBeginningOfBuffer );
        }
#if defined ( RAKI_USING_XAUDIO2 )
        inline const u32 getBufferIndex() const
        {
            return m_bufferIndex;
        }
#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )
        inline const void * getDataBuffer() const 
        {
            return m_dataBuffer;
        }

        inline const u32 getBufferSampleSize() const 
        {
            return m_bufferSampleSize;
        }
#endif 

        inline const u64 getMeasuredSamplesAtBeginningOfBuffer() const
        {
            return m_streamBufferInfo->getMeasuredSamplesAtBeginningOfBuffer();
        }

        inline const bool isActiveBuffer() const
        {
            return m_streamBufferInfo->isActiveBuffer( this );
        }

        inline const bool isLastBuffer() const
        {
            return m_isLastBuffer;
        }

        inline const void * getClientData() const
        {
            return m_clientData;
        }

    protected:

        inline void setStreamBufferInfo( StreamBufferInfo * const _streamBufferInfo )
        {
            m_streamBufferInfo = _streamBufferInfo;
        }

    private:
        u64 m_sourceIndex;
        u64 m_sourceSamplePositionBeforeSubmit;
#if defined ( RAKI_USING_XAUDIO2 )
         u32 m_bufferIndex;
#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )
        const void * m_dataBuffer;
        u32 m_bufferSampleSize;
#endif 
//        u32 m_nbBuffersQueued;
        const void * m_clientData;
        StreamBufferInfo * m_streamBufferInfo;
        bool m_isLastBuffer;
    };

    StreamBufferInfo();

    void reinit();

    Info * getNextReadInfo(); // for the moment used only on PS3 for synchronous update

    Info * getNextWriteInfo(); // used when pulling and submitting data 

    inline const u64 getMeasuredSamplesAtBeginningOfBuffer() const
    {
        return m_measuredSamplesAtBeginningOfBuffer;
    }

    inline void setActiveBuffer( Info * const _info , const u64 _measuredSamplesAtBeginningOfBuffer )
    {
        //RAKI_OUTPUT("0x%x StreamBufferInfo::setActiveBuffer _info 0x%x _measuredSamplesAtBeginningOfBuffer %d\n", this, _info, _measuredSamplesAtBeginningOfBuffer );

        m_measuredSamplesAtBeginningOfBuffer = _measuredSamplesAtBeginningOfBuffer;

        m_activeBufferInfo = _info;
    }

    inline const bool isActiveBuffer( const Info * _info ) const
    {
        return m_activeBufferInfo == _info;
    }

    inline const bool isLastBuffer( const Info * _info ) const
    {
        return m_activeBufferInfo == _info;
    }


    inline void updateStreamPosition( const void * _clientData, const u64 _samplePosition )
    {
        if ( m_streamUpdateClient )
            m_streamUpdateClient->updateStreamPosition( _clientData, _samplePosition );
    }

    inline void setStreamUpdateClient( StreamUpdateClient * _streamUpdateClient )
    {
        m_streamUpdateClient = _streamUpdateClient;
    }

private:

    u64 m_measuredSamplesAtBeginningOfBuffer;
    StreamUpdateClient * m_streamUpdateClient;
    Info * m_activeBufferInfo;
    Info m_infoArray[ STREAMBUFFERINFO_NB_INFOSTRUCT ];
    u32 m_nextReadInfoIndex;
    u32 m_nextWriteInfoIndex;
};





} // namespace raki




#endif // RAKI_STREAMBUFFERINFO_H


