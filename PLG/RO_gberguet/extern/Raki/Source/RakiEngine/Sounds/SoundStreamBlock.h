#ifndef RAKI_SOUNDSTREAMBLOCK_H
#define RAKI_SOUNDSTREAMBLOCK_H




// #define SOUNDSTREAMBLOCK_MARKERNAMESIZE 39 // with this marker name size, SoundStreamBlock size is 64
// u64 m_byteSize;
// char m_marker[SOUNDSTREAMBLOCK_MARKERNAMESIZE + 1];


namespace raki
{


class SeekableStream;
struct SoundStreamBlock;



class MarkerContainer
{
public:
    virtual const char * getMarkerName( u32 _index ) const = 0;
    virtual u64 getMarkerOffset( u32 _index ) const = 0;
    virtual u32 getNbMarkers( u32 _index ) const = 0;
};


class BufferPositionInfo
{

private:

    MarkerContainer * m_markerContainer;
    u64 m_sampleBeginning;
    u64 m_sampleSize;
    u64 m_bufferPositionInfoIndex;

};






class DataBuffer
{
    u64 m_maxSize;
    void * m_buffer;
    u64 m_size;
    bool m_endBuffer;
    u64 m_synchronizationIndex;
};


struct SoundStreamBlock
{
    u64 m_sampleBegin;
    u64 m_sampleSize;
    SeekableStream * m_stream;
};






class CacheBuffer
{
    CacheBuffer();
    
    const u64 getRemainingSize() const;
    u64 read( void * _buffer, u64 _size );

protected:

    bool allocate( u64 _maxSize );
    void free();
    void * const getBuffer() const;
    void setCopiedBufferInfo( u64 _size );

private:

    u64 m_maxSize;
    u64 m_size;
    u64 m_nextReadOffset;
    void * m_buffer;
    bool m_readyToRead; // flag set to true by update thread, set to false when empty by reading thread (callback) 

};



class StreamCache
{
public:
    void update(); // this update is called by mainframe, and must have access to game variables (for stream pattern behavior) 

private:

    CacheBuffer m_cacheArray[2];
};


class SoundStreamBlockProvider
{
    virtual SoundStreamBlock * getNextBlock() = 0;
};




} // namespace raki




#endif // RAKI_SOUNDSTREAMBLOCK_H


