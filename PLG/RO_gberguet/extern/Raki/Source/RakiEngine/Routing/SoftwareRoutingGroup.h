#ifndef RAKI_SOFTWAREROUTINGGROUP_H
#define RAKI_SOFTWAREROUTINGGROUP_H




#include "RakiEngine/Routing/RoutingGroup.h"





namespace raki
{

    class SoftwareRoutingGroup 
        : public RoutingGroup
    {
    public:

        SoftwareRoutingGroup();

        virtual ~SoftwareRoutingGroup(); 

        virtual void resetVolumeUpdateFlag();

        virtual void internalSetDecibelVolume( f32 _decibelVolume );

        virtual f32 getDecibelVolume();

        virtual bool childrenNeedVolumeUpdate();

    private:

        f32 m_decibelVolume;

        bool m_childrenNeedVolumeUpdate;

    };



} // namespace raki




#endif // RAKI_SOFTWAREROUTINGGROUP_H


