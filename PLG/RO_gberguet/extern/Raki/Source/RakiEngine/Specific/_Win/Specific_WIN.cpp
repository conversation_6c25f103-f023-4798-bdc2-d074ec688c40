#include "Precompiled.h"


#include "RakiEngine/Specific/_WIN/Specific_WIN.h"




#include "RakiEngine/Data/Serialization/Serializer.h"
#include "RakiEngine/Data/Stream/Stream.h"
#include "RakiEngine/Data/WaveFile/WaveFile.h"








#ifndef RAKI_PLATFORM_WIN32
#error ("platform specific...")
#endif // RAKI_PLATFORM_WIN32






#include "RakiEngine/Data/WaveFile/RiffChunkManager.h"





namespace raki
{


Specific::RewriteRiffFile::RewriteRiffFile( bool _changeToBigEndians )
    : m_changeToBigEndians( _changeToBigEndians )
    , m_waveFile( NULL )
{

}


Specific::RewriteRiffFile::~RewriteRiffFile()
{
    if ( m_waveFile )
        RAKI_DELETE( m_waveFile );

    m_waveFile = NULL;
};


bool Specific::RewriteRiffFile::read( SeekableStream * _inputStream )
{
    RAKI_ASSERT( !m_waveFile );
    RAKI_ASSERT( _inputStream );
    RAKI_ASSERT( _inputStream->isReading() );

    raki::RiffChunkManager::singleton().setIsWritingToBigEndians( m_changeToBigEndians );
    raki::RiffChunkManager::singleton().setShouldReadDataBlockForWaveData( true );

    Serializer readSerializer( _inputStream );

    m_waveFile = RAKI_NEW( WaveFile );
    m_waveFile->read( readSerializer );

    if ( !m_waveFile->getChunkData( "data" ) )
        return false;

    if ( !m_waveFile->getChunkData( "fmt " ) )
        return false;

    if ( m_changeToBigEndians )
    {
        if ( !m_waveFile->changeToBigEndian() )
            return false;
    }

    m_waveFile->recalculateRiffAndListSizes();

    return true;
}

bool Specific::RewriteRiffFile::write( SeekableStream * _outputStream )
{
    RAKI_ASSERT( m_waveFile );
    RAKI_ASSERT( _outputStream );
    RAKI_ASSERT( _outputStream->isWriting() );

    if ( !m_waveFile->getChunkData( "data" ) )
        return false;

    if ( !m_waveFile->getChunkData( "fmt " ) )
        return false;

    Serializer writeSerializer( _outputStream );

    m_waveFile->write( writeSerializer );

    if ( writeSerializer.encounteredError() )
        return false;

    return true;
}





} // namespace raki

