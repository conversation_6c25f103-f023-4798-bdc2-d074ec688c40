#include "Precompiled.h"



#ifdef RAKI_PLATFORM_PS3
#include <cell/audio.h>
#include <cell/mstream.h>
#include <cell/sysmodule.h>
#include <cell/spurs/control.h>
#include <sys/ppu_thread.h>
#include <sys/spu_initialize.h>

#include <sysutil/sysutil_common.h>
#endif // RAKI_PLATFORM_PS3




#include "RakiEngine/Initializer/TestInitializer.h"






#include "RakiEngine/Initializer/EngineInitializer.h"
#include "RakiEngine/System/ThreadBase/ThreadBase.h"





#if defined ( RAKI_PLATFORM_PS3 )


//#define MS_THREADED_SAMPLE

#ifndef MS_THREADED_SAMPLE
/**********************************************************************************/
// SPURS information
/**********************************************************************************/
#define				SPURS_SPU_NUM	1
#define				SPU_THREAD_GROUP_PRIORITY		250
CellSpurs			spurs __attribute__((aligned (128)));
#endif



#elif defined ( RAKI_PLATFORM_WII )

#include <revolution/vi.h>
#include "RakiEngine/Specific/_Wii/AudioCallbackManager_Wii.h"

#endif 





namespace raki
{





#if defined ( RAKI_PLATFORM_PS3 )



    /**********************************************************************************
    systemCallback
    sysutil callback to catch the exit request
    **********************************************************************************/
    static void systemCallback(const uint64_t status, const uint64_t param, void *userdata)
    {
        //Remove compiler warnings
        (void)param;
        (void)userdata;
/*
        switch (status) 
        {
        case CELL_SYSUTIL_REQUEST_EXITGAME:
            printf("system notification: CELL_SYSUTIL_REQUEST_EXITGAME\n");
            s_receivedExitGameRequest = true;
            break;

        default:
            //printf("system notification: unknown status\n");
            break;
        }*/
    }

#endif 












    TestInitializer::TestInitializer()
#if defined ( RAKI_USING_XAUDIO2 )
        : m_masteringVoice( NULL )
        , m_xaudio2( NULL )
#elif defined ( RAKI_PLATFORM_PS3 )
        : m_audioPortNumber( 0 )
        , m_multistreamMemory( NULL )
        , m_mp3Memory( NULL )
#elif defined ( RAKI_PLATFORM_WII )
        : m_axBuffer( NULL )
#endif 
    {
        Memory::init( &m_allocator );

        EngineInitializer::InitStruct initStruct;

#if defined ( RAKI_USING_XAUDIO2 )


#ifdef RAKI_PLATFORM_WIN32
        CoInitializeEx( NULL, COINIT_MULTITHREADED );
#endif // RAKI_PLATFORM_WIN32

        UINT32 flags = 0;
#ifdef WORK_TARGET_DEBUG
        flags |= XAUDIO2_DEBUG_ENGINE;
#endif
        HRESULT hr = XAudio2Create( &m_xaudio2, flags ); 
        
        RAKI_ASSERT( hr == S_OK );


        // Create a mastering voice
        hr = m_xaudio2->CreateMasteringVoice( &m_masteringVoice );
        RAKI_ASSERT( hr == S_OK );

        initStruct.m_xaudio2 = m_xaudio2;

#endif // ( RAKI_USING_XAUDIO2 )

        EngineInitializer::createSingleton();

#if defined ( RAKI_PLATFORM_PS3 )
        bool initOK = init();
        RAKI_ASSERT( initOK );
#endif // defined ( RAKI_PLATFORM_PS3 )


#if defined ( RAKI_PLATFORM_WII )
        // initialize AI & AX

        AIInit(NULL);
        m_axBuffer = Memory::mallocMem2Aligned32( AXGetMemorySize( AX_MAX_VOICES ), Memory::system );
        AXInitSpecifyMem( AX_MAX_VOICES, m_axBuffer );
    
        AXRegisterCallback( &AudioCallbackManager::staticAudioCallback );

#endif // ( RAKI_PLATFORM_WII )


        EngineInitializer::singleton().initialize( &initStruct );
    }


    TestInitializer::~TestInitializer()
    {
        //Thread::sleep( 50 );

#if defined ( RAKI_PLATFORM_WII )
        // uninitialize AI & AX
        
        AXRegisterCallback( NULL );
        AXQuit();

        Memory::freeMem2Aligned32( m_axBuffer );
        m_axBuffer = NULL;
        
#endif // ( RAKI_PLATFORM_WII )

#if defined ( RAKI_PLATFORM_PS3 )
        uninit();
#endif // defined ( RAKI_PLATFORM_PS3 )

        EngineInitializer::singleton().uninitialize();
        EngineInitializer::destroySingleton();

#ifdef RAKI_PLATFORM_WIN32
        CoUninitialize();
#endif // RAKI_PLATFORM_WIN32

        Memory::uninit();
    }


#if defined ( RAKI_PLATFORM_PS3 )


    const static int maxNumberOfStreams = 512;

    bool TestInitializer::init()
    {
        if ( !loadModules() )
            return false;

        sys_spu_initialize(6, 0);

        int ret = cellSysutilRegisterCallback(0, systemCallback, NULL);
        if (ret != CELL_OK) 
            return false;

        //	cellMSSystemConfigureSysUtilEx returns the following data:
        //	Bits 0-3:	Number of available output channels
        //	Bit    4:	Dolby On status
        //	Bit    5:	DTS On status
        //unsigned int retSysUtil = cellMSSystemConfigureSysUtilEx(CELL_MS_AUDIOMODESELECT_SUPPORTSLPCM | CELL_MS_AUDIOMODESELECT_SUPPORTSDOLBY | CELL_MS_AUDIOMODESELECT_SUPPORTSDTS | CELL_MS_AUDIOMODESELECT_PREFERDOLBY);
        unsigned int retSysUtil = cellMSSystemConfigureSysUtilEx( CELL_MS_AUDIOMODESELECT_SUPPORTSLPCM );
        unsigned int numChans = (retSysUtil & 0x0000000F);
        unsigned int dolbyOn = (retSysUtil & 0x00000010) >> 4;
        unsigned int dtsOn = (retSysUtil & 0x00000020) >> 5;
        printf("Number Of Channels: %u\n", numChans);
        printf("Dolby enabled: %u\n", dolbyOn);
        printf("DTS enabled: %u\n", dtsOn);

        ret = cellAudioInit();
        if ( ret !=CELL_OK )
            return false;


        CellAudioPortParam audioParam = { 0 };
        audioParam.nChannel = CELL_AUDIO_PORT_8CH; //CELL_AUDIO_PORT_2CH;
        audioParam.nBlock = CELL_AUDIO_BLOCK_8;

        ret = cellAudioPortOpen(&audioParam, &m_audioPortNumber);
        if ( ret != CELL_OK )
            return false; 


        CellAudioPortConfig portConfig;

        // get port config.
        ret = cellAudioGetPortConfig(m_audioPortNumber, &portConfig);
        printf("cellAudioGetPortConfig() : %d\n", ret);
        if ( ret != CELL_OK )
            return false;


        ret = cellMSSystemConfigureLibAudio(&audioParam, &portConfig);
        if ( ret != CELL_OK )
            return false;





       // Initialise MultiStream

        CellMSSystemConfig multistreamSystemConfig = { 0 };
        multistreamSystemConfig.channelCount = maxNumberOfStreams;
        multistreamSystemConfig.subCount = 31;
        multistreamSystemConfig.dspPageCount = 5;
        multistreamSystemConfig.flags = CELL_MS_NOFLAGS;
        int nMemoryNeeded = cellMSSystemGetNeededMemorySize( &multistreamSystemConfig );

        m_multistreamMemory = Memory::mallocAligned( nMemoryNeeded, 128, Memory::system );
        Memory::memZero( m_multistreamMemory, nMemoryNeeded );
        if ( !m_multistreamMemory )
            return false;

#ifdef MS_THREADED_SAMPLE
        ret = cellMSSystemInitSPUThread( m_multistreamMemory, &multistreamSystemConfig, 100);
        if ( ret )
            return false;

#else // MS_THREADED_SAMPLE

        // Init SPURS
        sys_ppu_thread_t	thread_id;
        int					ppu_thr_prio __attribute__((aligned (128)));  // information for the reverb

        sys_ppu_thread_get_id(&thread_id);
        ret = sys_ppu_thread_get_priority(thread_id, &ppu_thr_prio);
        if ( ret != CELL_OK )
            return false;
        printf(" thread_id = %d, ppu_thr_prio = %d\n", (int)thread_id, ppu_thr_prio );

        // Keep the SPURS_SPU_NUM value as low as possible (MultiStream uses a maximum of 1 SPU) to reduce 
        // priority conflicts when SPURS SPU thread groups are context switched in and out by the PS3 operating system.
        ret = cellSpursInitialize(&spurs, SPURS_SPU_NUM, SPU_THREAD_GROUP_PRIORITY, ppu_thr_prio-1, false);
        if(ret != CELL_OK)
        {
            printf( "******** ERROR cellSpursInitialize = 0x%x\n", ret );
            while(1){};
        }

        // Initialise SPURS MultiStream version
        uint8_t prios[8] = {1, 0, 0, 0, 0, 0, 0, 0};

        if ( cellMSSystemInitSPURS( m_multistreamMemory, &multistreamSystemConfig, &spurs, &prios[0]) )
            return false;
#endif // MS_THREADED_SAMPLE


        // Initialise MP3
        int mp3SizeNeeded = cellMSMP3GetNeededMemorySize( 256 );//maxNumberOfStreams );
        m_mp3Memory = (int*)Memory::malloc( mp3SizeNeeded, Memory::system );
        if ( !m_mp3Memory )
            return false;

        ret = cellMSMP3Init( 256/*maxNumberOfStreams*/, m_mp3Memory ); 
        if ( ret )
            return false;

        // Setup the volumes on sub buss 1. By default all sub busses route to the master bus
        int SUBNUM = 1;
        int PLAYSUB = SUBNUM | CELL_MS_BUS_FLAG;
        // bus volumes. (Maximum volume for each speaker)
        float fBusVols[64] = {1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f};
        cellMSCoreSetVolume64(PLAYSUB, CELL_MS_WET, fBusVols);

        // Setup the volumes on the master buss
        cellMSCoreSetVolume64(CELL_MS_MASTER_BUS, CELL_MS_DRY, fBusVols);


        m_multiStreamUpdateThread = RAKI_NEW( MultiStreamUpdateThread )( m_audioPortNumber );

        m_multiStreamUpdateThread->startThread();

        return true;
    }


    bool TestInitializer::uninit()
    {
        m_multiStreamUpdateThread->requestEnd();

        while ( m_multiStreamUpdateThread->isRunning() )
            Thread::sleep( 50 );

        delete m_multiStreamUpdateThread;

        m_multiStreamUpdateThread = NULL;

        bool unloadedModules = unloadModules();

        Memory::free( m_mp3Memory );

        return unloadedModules;
    }


    bool TestInitializer::loadModules()
    {
        printf( "\nLoading libfs\n" );
        int ret = cellSysmoduleLoadModule( CELL_SYSMODULE_FS );
        if ( ret < 0 )
        {
            printf( "\nError loading module FS!!!\n" );
            return false;
        }

        printf( "\nLoading libusbd\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_USBD );
        if ( ret < 0 )
        {
            printf( "\nError loading module USBD!!!\n" );
            return false;
        }

        printf( "\nLoading libnet\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_NET );
        if ( ret < 0 )
        {
            printf( "\nError loading module NET!!!\n" );
            return false;
        }

        printf( "\nLoading libio\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_IO );
        if ( ret < 0 )
        {
            printf( "\nError loading module IO!!!\n" );
            while(1){};
        }

        printf( "\nLoading libaudio\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_AUDIO );
        if ( ret < 0 )
        {
            printf( "\nError loading module AUDIO!!!\n" );
            return false;
        }

        printf( "\nLoading libresc\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_RESC );
        if ( ret < 0 )
        {
            printf( "\nError loading module RESC!!!\n" );
            return false;
        }

#ifndef MS_THREADED_SAMPLE
        printf( "\nLoading libspurs\n" );
        ret = cellSysmoduleLoadModule( CELL_SYSMODULE_SPURS );
        if ( ret < 0 )
        {
            printf( "\nError loading module SPURS!!!\n" );
            return false;
        }
#endif // !MS_THREADED_SAMPLE

        return true;
    }


    bool TestInitializer::unloadModules()
    {
        printf( "\n Unloading libfs\n" );
        int ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_FS );
        if ( ret < 0 )
        {
            printf( "\nError unloading module FS!!!\n" );
            return false;
        }

        printf( "\n Unloading libusbd\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_USBD );
        if ( ret < 0 )
        {
            printf( "\nError unloading module USBD!!!\n" );
            return false;
        }

        printf( "\n Unloading libnet\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_NET );
        if ( ret < 0 )
        {
            printf( "\nError unloading module NET!!!\n" );
            return false;
        }

        printf( "\n Unloading libio\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_IO );
        if ( ret < 0 )
        {
            printf( "\nError unloading module IO!!!\n" );
            while(1){};
        }

        printf( "\n Unloading libaudio\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_AUDIO );
        if ( ret < 0 )
        {
            printf( "\nError unloading module AUDIO!!!\n" );
            return false;
        }

        printf( "\n Unloading libresc\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_RESC );
        if ( ret < 0 )
        {
            printf( "\nError unloading module RESC!!!\n" );
            return false;
        }

#ifndef MS_THREADED_SAMPLE
        printf( "\n Unloading libspurs\n" );
        ret = cellSysmoduleUnloadModule( CELL_SYSMODULE_SPURS );
        if ( ret < 0 )
        {
            printf( "\nError unloading module SPURS!!!\n" );
            return false;
        }
#endif

        return true;
    }


    bool init();
    void uninit();
#endif // ( RAKI_USING_XAUDIO2 )

} // namespace raki



