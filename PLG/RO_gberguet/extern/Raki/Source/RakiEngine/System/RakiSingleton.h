#ifndef RAKISINGLETON_H
#define RAKISINGLETON_H


#define RAKI_DECLARE_SINGLETON(ClassName) 														\
private:																						\
    static ClassName * ms_singleton;															\
public: 																						\
    static void createSingleton(); 																\
    static void destroySingleton(); 															\
    static ClassName& singleton()


#define RAKI_IMPLEMENT_SINGLETON(ClassName) 													\
    raki::ClassName * raki::ClassName::ms_singleton = NULL;									    \
    void raki::ClassName::createSingleton()														\
    {																							\
        RAKI_ASSERT( !ms_singleton );                                                           \
        ms_singleton = RAKI_NEW(ClassName);														\
    }																							\
    void raki::ClassName::destroySingleton() 													\
    { 																							\
        RAKI_ASSERT( ms_singleton ); 		                                                    \
        RAKI_DELETE( ms_singleton ); 															\
        ms_singleton = NULL; 																	\
    } 																							\
    raki::ClassName& raki::ClassName::singleton()												\
    {																							\
        RAKI_ASSERT( ms_singleton );                                                            \
        return *ms_singleton;                                                                   \
    }																								


#endif // RAKISINGLETON_H
