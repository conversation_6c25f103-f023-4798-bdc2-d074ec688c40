#include "Precompiled.h"



#if defined ( RAKI_PLATFORM_PS3 )
    #include <sys/timer.h>
#elif defined ( RAKI_PLATFORM_PS5 )
    #include <kernel.h>
#elif defined ( RAKI_PLATFORM_NINTENDO )
    #include <nn/os/os_Thread.h>
#endif // defined ( RAKI_PLATFORM_PS3 )



#if !defined ( RAKI_PLATFORM_WII )




#include "RakiEngine/System/AudioCallbackManager/AudioCallbackManager.h"













RAKI_IMPLEMENT_SINGLETON( AudioCallbackManager );










namespace raki
{


    // AudioCallbackDisabler implementation 


    AudioCallbackDisabler::AudioCallbackDisabler()
    {
        RAKI_ASSERT( AudioCallbackManager::singleton().isAudioCallbackEnabled() );

        AudioCallbackManager::singleton().disableAudioCallback();

        while ( AudioCallbackManager::singleton().hasEnteredAudioCallback() )
        {
            RAKI_OUTPUT("AudioCallbackDisabler::AudioCallbackDisabler yielding...");
#if defined ( RAKI_PLATFORM_PS3 )
            sys_timer_usleep( 100 );
#elif defined ( RAKI_PLATFORM_PS5 )
            sceKernelUsleep(100);
#elif defined ( RAKI_PLATFORM_NINTENDO )
            nn::os::YieldThread();
#else 
            Yield();
#endif
        }
    }


    AudioCallbackDisabler::~AudioCallbackDisabler()
    {
        AudioCallbackManager::singleton().reenableAudioCallback();
    }




    // AudioCallbackObject implementation 


    AudioCallbackObject::AudioCallbackObject()
        : m_isActive( false )
    {
        AudioCallbackManager::singleton().enterAudioCallback();

        m_isActive = AudioCallbackManager::singleton().isAudioCallbackEnabled();
    }


    AudioCallbackObject::~AudioCallbackObject()
    {
        AudioCallbackManager::singleton().leaveAudioCallback();
    }





    // AudioCallbackManager implementation 

    
    AudioCallbackManager::AudioCallbackManager()
        : m_callbackDisabled( 0 )
#if defined ( RAKI_PLATFORM_PS3 )
        , m_enteredAudioCallback( 0 )
#else // defined ( RAKI_PLATFORM_PS3 )
        , m_enteredAudioCallbackCounter( 0 )
#endif // defined ( RAKI_PLATFORM_PS3 )
    {
    }

    
    AudioCallbackManager::~AudioCallbackManager()
    {
    }



} // namespace raki



#endif // !defined ( RAKI_PLATFORM_WII )
