#ifndef RAKI_STREAMINGTHREAD_H
#define RAKI_STREAMINGTHREAD_H




#include "RakiEngine/System/Containers/CircularCommandList.h"
#include "RakiEngine/System/Streaming/StreamingRequest.h"
#include "RakiEngine/System/ThreadBase/ThreadBase.h"



#define STREAMINGTHREAD_COMMANDLIST_SIZE                256
#define STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE      256





namespace raki
{


class SeekableStream;




class StreamingThread 
    : public ThreadBase
{

public:

    StreamingThread();

    virtual ~StreamingThread();

    void start();
    
    void stop();

    virtual void run();

    StreamingRequest * addReadRequest( SeekableStream * _stream, const u64 _position, const u64 _sizeRequested, void * _buffer );

    // this is called when client wants to cancel a stream request which has been made - client must still wait till request getState returns something else than readRequested 
    void cancelReadRequest( StreamingRequest * const _request );

    // this is called by client AFTER getState returns something else than readRequested 
    void closeReadRequest( StreamingRequest * const _request );

    // this can be overwritten by client so client can disable requests from being executed 
    virtual bool canProcessRequest();

private:

    enum RequestType
    {
        read,
        cancel,
        close
    };

    struct Request
    {
        Request( const RequestType _type, StreamingRequest * const _streamingRequest )
            : m_type( _type )
            , m_streamingRequest( _streamingRequest )
        {
        }

        RequestType m_type;
        StreamingRequest * m_streamingRequest;
    };

    StreamingRequest m_streamingRequestArray[STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE];

    StreamingRequest * m_processingSlots[STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE];

    CircularCommandList<Request> m_requestCommandList;

    volatile bool m_stopThreadRequested;

};



} // namespace raki


#endif // RAKI_STREAMINGTHREAD_H