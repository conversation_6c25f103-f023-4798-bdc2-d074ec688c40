#ifndef RAKISTLALLOCATOR_H
#define RAKISTLALLOCATOR_H

#include "RakiEngine/System/Memory/RakiMemory.h"

namespace raki
{
    template<typename T, Memory::Type MemType>
    class StlAllocator
    {
    public:

        using value_type = T;
        using size_type = size_t;
        using is_always_equal = std::true_type;

        template <typename U>
        struct rebind
        {
            using other = StlAllocator<U, MemType>;
        };

        constexpr StlAllocator() noexcept {}
        constexpr StlAllocator(const StlAllocator&) noexcept = default;

        // Allow copy construction from any other type (Type agnostic allocator)
        template <typename U, Memory::Type OtherMemType>
        constexpr StlAllocator(const StlAllocator<U, OtherMemType>& other) noexcept {}

        constexpr bool operator == (const StlAllocator&) const noexcept { return true; }
        constexpr bool operator != (const StlAllocator&) const noexcept { return false; }

        inline value_type* allocate(size_type cnt, const void* = 0)
        {
            void* ptr = Memory::malloc(cnt * sizeof(value_type), MemType);
            RAKI_ASSERT(((u64)ptr) % 4 == 0);
            return static_cast<value_type*>(ptr);
        }

        inline void deallocate(value_type* p, size_type)
        {
            Memory::free(p);
        }
    };
}

#endif // RAKISTLALLOCATOR_H
