#ifndef RAKI_SYSTEMALLOCATOR_H
#define RAKI_SYSTEMALLOCATOR_H



#include "RakiEngine/System/Memory/Allocator.h"



#include <map>


// this is the default system allocator, which uses malloc and free and which SHOULD NOT BE USED other than in tests
// use the game's allocator instead 


namespace raki
{


class SystemAllocator
    : public Allocator
{
public:

    SystemAllocator();
    virtual ~SystemAllocator();

    virtual void * malloc( const u64 _size, const Memory::Type _type );
    virtual void free( void * _ptr );

#if defined ( RAKI_PLATFORM_PS3 )

    virtual void * mallocAligned( const u64 _size, const u64 _alignment, const Memory::Type _type );
    virtual void freeAligned( void * _ptr );

#elif defined ( RAKI_PLATFORM_WII )

    virtual void * mallocMem2Aligned32( const u64 _size, const Memory::Type _type );
    virtual void freeMem2Aligned32( void * _ptr );

#elif defined ( RAKI_PLATFORM_XB360 )

    virtual void * mallocPhysicalAligned( const u64 _size, const Memory::Type _type );
    virtual void freePhysicalAligned( void * _ptr );

#endif 

    virtual const u64 getUsedSize();

    virtual void memcpy( void * _dest, const void * _src, u64 _size );
    virtual void memZero( void * _dest, u64 _size );

private:

    typedef std::map< u64, u64 > MemoryContainer;
    //MemoryContainer m_allocated;

#if defined ( RAKI_PLATFORM_PS3 )
    MemoryContainer m_allocatedAligned;
#endif // ( RAKI_USING_XAUDIO2 )


#if defined ( RAKI_PLATFORM_WII )
    //MemoryContainer m_allocatedAligned;
#endif 

};



}










#endif // RAKI_SYSTEMALLOCATOR_H

