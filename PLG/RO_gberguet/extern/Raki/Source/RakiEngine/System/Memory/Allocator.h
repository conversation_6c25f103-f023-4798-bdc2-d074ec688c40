#ifndef RAKI_ALLOCATOR_H
#define RAKI_ALLOCATOR_H



namespace raki
{


class Allocator
{
public:

    virtual void * malloc( const u64 _size, const Memory::Type _type ) = 0;
    virtual void free( void * _ptr ) = 0;

#if defined ( RAKI_PLATFORM_PS3 )

    virtual void * mallocAligned( const u64 _size, const u64 _alignment, const Memory::Type _type ) = 0;
    virtual void freeAligned( void * _ptr ) = 0;

#elif defined ( RAKI_PLATFORM_WII )

    virtual void * mallocMem2Aligned32( const u64 _size, const Memory::Type _type ) = 0;
    virtual void freeMem2Aligned32( void * _ptr ) = 0;

#elif defined ( RAKI_PLATFORM_XB360 )

    virtual void * mallocPhysicalAligned( const u64 _size, const Memory::Type _type ) = 0;
    virtual void freePhysicalAligned( void * _ptr ) = 0;

#endif 

    virtual const u64 getUsedSize() = 0;

    virtual void memcpy( void * _dest, const void * _src, u64 _size ) = 0;
    virtual void memZero( void * _dest, u64 _size ) = 0;
};




} // namespace raki










#endif // RAKI_ALLOCATOR_H

