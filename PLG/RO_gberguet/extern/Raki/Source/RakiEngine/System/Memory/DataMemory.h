#ifndef RAKI_DATAMEMORY_H
#define RAKI_DATAMEMORY_H



// this is the default memory system used by <PERSON><PERSON>, for RAKI_NEW and RAKI_DELETE



namespace raki
{


class Allocator;


class RakiMemory 
{

public:

    static void init( Allocator * _allocator );
    static void uninit();

    static void * malloc( const size_t _size, const Memory::Type _type );
    static void free( void * _ptr );

    static void memcpy( void * _dest, const void * _src, size_t _size );
    static void memZero( void * _dest, size_t _size );

    static bool isBufferAligned( void * _buffer, size_t _alignment );

private:

    static Allocator * ms_allocator;

};



}










#endif // RAKI_DATAMEMORY_H

