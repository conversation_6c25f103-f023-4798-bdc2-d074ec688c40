#include "Precompiled.h"



#include "RakiEngine/System/ThreadBase/ThreadBase.h"



#include "RakiEngine/System/RakiThread.h"




namespace raki
{


ThreadBase::ThreadBase()
    : m_isRunning( false )
{

}


ThreadBase::~ThreadBase()
{
}


void ThreadBase::startThread()
{
    bool begun = Thread::beginThread( this );

    RAKI_ASSERT( begun );
}



void ThreadBase::internalRun()
{
    RAKI_ASSERT( !isRunning() );

    setIsRunning( true );

    preRun();

    run();

    postRun();

    setIsRunning( false );
}


void ThreadBase::preRun()
{
}


void ThreadBase::postRun()
{
}



}
