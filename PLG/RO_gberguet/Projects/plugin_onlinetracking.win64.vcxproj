<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Final|x64">
      <Configuration>Final</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Retail|x64">
      <Configuration>Retail</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{58AF9D59-88DA-8D2A-645F-3DD0E5F9B974}</ProjectGuid>
    <DefaultLanguage>en-US</DefaultLanguage>
    <RootNamespace>Plugin_OnlineTracking</RootNamespace>
    <ProjectName>Plugin_OnlineTracking</ProjectName>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <MSBuildExtensionsPath>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\</MSBuildExtensionsPath>
    <VCInstallDir_170>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\VC\</VCInstallDir_170>
    <VCTargetsPath17>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\Microsoft\VC\v170\</VCTargetsPath17>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(Platform)'=='x64'">
    <AdditionalVCTargetsPath>e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\msbuild\vs2022\</AdditionalVCTargetsPath>
    <_VCTargetsPathForToolset>$(AdditionalVCTargetsPath)</_VCTargetsPathForToolset>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <UCRTContentRoot>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UCRTContentRoot>
    <UniversalCRTSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UniversalCRTSdkDir_10>
    <WindowsSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</WindowsSdkDir_10>
    <WindowsSdkDir>$(WindowsSdkDir_10)</WindowsSdkDir>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>plugin_onlinetracking</TargetName>
    <OutDir>lib\win64\debug\</OutDir>
    <IntDir>tmp\win64\plugin_onlinetracking\debug_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\debug\plugin_onlinetracking.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <OutDir>lib\win64\final\</OutDir>
    <IntDir>tmp\win64\plugin_onlinetracking\final_vs2022\</IntDir>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>plugin_onlinetracking</TargetName>
    <OutDir>lib\win64\release\</OutDir>
    <IntDir>tmp\win64\plugin_onlinetracking\release_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\release\plugin_onlinetracking.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <OutDir>lib\win64\retail\</OutDir>
    <IntDir>tmp\win64\plugin_onlinetracking\retail_vs2022\</IntDir>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_DEBUG;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_DEBUG;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\pugixml\src;..\src;..\src\tools\plugins\plugin_onlinetracking</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\plugin_onlinetracking\debug_vs2022\plugin_onlinetracking_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <OptimizeReferences>false</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\debug\plugin_onlinetracking.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\pugixml\src;..\src;..\src\tools\plugins\plugin_onlinetracking</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\plugin_onlinetracking\final_vs2022\plugin_onlinetracking_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_MICROSOFT;ITF_RELEASE;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\pugixml\src;..\src;..\src\tools\plugins\plugin_onlinetracking</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\plugin_onlinetracking\release_vs2022\plugin_onlinetracking_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\release\plugin_onlinetracking.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_RETAIL;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;RETAIL;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\pugixml\src;..\src;..\src\tools\plugins\plugin_onlinetracking</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\plugin_onlinetracking\retail_vs2022\plugin_onlinetracking_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\src\tools\plugins\Plugin_OnlineTracking\OnlineTrackingPlugin.h" />
    <ClInclude Include="..\src\tools\plugins\Plugin_OnlineTracking\StringUtils.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\tools\plugins\Plugin_OnlineTracking\OnlineTrackingPlugin.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\tools\plugins\Plugin_OnlineTracking\StringUtils.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
