<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Final|x64">
      <Configuration>Final</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Retail|x64">
      <Configuration>Retail</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FDC5D6F0-BE57-EBB6-6FDE-956E3B2FFFD4}</ProjectGuid>
    <DefaultLanguage>en-US</DefaultLanguage>
    <RootNamespace>ITF_Gameplay</RootNamespace>
    <ProjectName>ITF_Gameplay</ProjectName>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <MSBuildExtensionsPath>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\</MSBuildExtensionsPath>
    <VCInstallDir_170>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\VC\</VCInstallDir_170>
    <VCTargetsPath17>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\Microsoft\VC\v170\</VCTargetsPath17>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(Platform)'=='x64'">
    <AdditionalVCTargetsPath>e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\msbuild\vs2022\</AdditionalVCTargetsPath>
    <_VCTargetsPathForToolset>$(AdditionalVCTargetsPath)</_VCTargetsPathForToolset>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <UCRTContentRoot>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UCRTContentRoot>
    <UniversalCRTSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UniversalCRTSdkDir_10>
    <WindowsSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</WindowsSdkDir_10>
    <WindowsSdkDir>$(WindowsSdkDir_10)</WindowsSdkDir>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>itf_gameplay</TargetName>
    <OutDir>lib\win64\debug\</OutDir>
    <IntDir>tmp\win64\itf_gameplay\debug_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\debug\itf_gameplay.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <TargetName>itf_gameplay</TargetName>
    <OutDir>lib\win64\final\</OutDir>
    <IntDir>tmp\win64\itf_gameplay\final_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\final\itf_gameplay.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>itf_gameplay</TargetName>
    <OutDir>lib\win64\release\</OutDir>
    <IntDir>tmp\win64\itf_gameplay\release_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\release\itf_gameplay.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <TargetName>itf_gameplay</TargetName>
    <OutDir>lib\win64\retail\</OutDir>
    <IntDir>tmp\win64\itf_gameplay\retail_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\retail\itf_gameplay.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_DEBUG;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_DEBUG;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\dxsdk\include;..\extern\pugixml\src;..\extern\raki\source;..\extern\wwise\include;..\src;..\src\gameplay</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_gameplay.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_gameplay\debug_vs2022\ITF_Gameplay.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_gameplay\debug_vs2022\itf_gameplay_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <OptimizeReferences>false</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\debug\itf_gameplay.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\dxsdk\include;..\extern\pugixml\src;..\extern\raki\source;..\extern\wwise\include;..\src;..\src\gameplay</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_gameplay.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_gameplay\final_vs2022\ITF_Gameplay.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_gameplay\final_vs2022\itf_gameplay_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\final\itf_gameplay.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_MICROSOFT;ITF_RELEASE;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\dxsdk\include;..\extern\pugixml\src;..\extern\raki\source;..\extern\wwise\include;..\src;..\src\gameplay</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_gameplay.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_gameplay\release_vs2022\ITF_Gameplay.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_gameplay\release_vs2022\itf_gameplay_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\release\itf_gameplay.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_RETAIL;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;RETAIL;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\dxsdk\include;..\extern\pugixml\src;..\extern\raki\source;..\extern\wwise\include;..\src;..\src\gameplay</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_gameplay.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_gameplay\retail_vs2022\ITF_Gameplay.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_gameplay\retail_vs2022\itf_gameplay_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\retail\itf_gameplay.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\src\gameplay\AI\Actions\AIAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIBezierAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIBounceAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIBounceToLayerAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIBumperAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIDestroyAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIDisableAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIFadeAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIFallAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIFallNoPhysAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIFollowActorAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIFollowCollideActorAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIIdleAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIJumpAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIJumpAngleAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIJumpInDirAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIJumpToTargetAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIPerformHitAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIPlayAnimAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIPlayFXAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIReceiveHitAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AISendEventAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AISpawnAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIStickBoneAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIUTurnAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\AIWalkInDirAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsApexAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsFixedGravityAction.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIDeathBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIPlayActionsBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIReceiveHitBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIReplicateParentAnimBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AIRoamingBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\Behaviors\AISimplePlayAnimBehavior.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\AIBTFacts.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BehaviorTree.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\Blackboard.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTAction.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionCopyFact.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionJumpToBack.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionJumpToTarget.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionPlayAnim.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionRemoveFact.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionSendEventToActor.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionSetFact.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionStayIdle.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionStayIdleLookAt.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTActionWalkToTarget.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDecider.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDeciderFactEqual.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDeciderHasActorsAlive.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDeciderHasFact.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDeciderHasPlayerBehind.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDeciderHasPlayerNear.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTDelay.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTNode.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTSequence.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\BTTimer.h" />
    <ClInclude Include="..\src\gameplay\AI\BTs\Fact.h" />
    <ClInclude Include="..\src\gameplay\AI\Utils\AIUtils.h" />
    <ClInclude Include="..\src\gameplay\AI\Utils\AnimationAtlas.h" />
    <ClInclude Include="..\src\gameplay\AI\Utils\EventActivateHandler.h" />
    <ClInclude Include="..\src\gameplay\AI\Utils\EventDelayHandler.h" />
    <ClInclude Include="..\src\gameplay\AI\Utils\TrajectoryProvider.h" />
    <ClInclude Include="..\src\gameplay\AnimationMarkers.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\AdditionalBehaviorsComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\AIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\BaseAIControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\BTAIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\GenericAIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\GroundAIControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\SimpleAIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\TimedSpawnerAIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\AI\WaypointComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\ActorSpawnComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\BounceOnPolylinePhysComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\CharacterDebuggerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\ComponentsUtils.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\ConstantMovementComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\DeformOnTrajectoryComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\ParticlePhysComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\PoolActorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\StickToPolylinePhysComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\TimedSpawnerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\TweenComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\TweenInstructions.h" />
    <ClInclude Include="..\src\gameplay\Components\Common\TweenInterpreter.h" />
    <ClInclude Include="..\src\gameplay\Components\Display\RenderBezierPatchCurveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Display\RenderSimpleAnimComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\AxisPolylineComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\HingePlatformComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\PendulumComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\PointsCollisionComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\PolylineComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\ProceduralPolyline.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\ProceduralSoftPlatformComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\RotatingPolylineComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\SoftPlatform.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\SoftPlatformComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\SolidPolylineComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Environment\SwingSoftPlatform.h" />
    <ClInclude Include="..\src\gameplay\Components\FX\BoxInterpolatorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\FX\CircleInterpolatorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\FX\InterpolatorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\FX\SpeedInputProviderComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\AABBPrefetchComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\AfterFxControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\AlwaysActiveActorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\AnimTriggeredComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\ArenaDoorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\BezierCurveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\CameraSubjectComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\CheckpointComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\CurveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\CurveFollowerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\DataErrorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\EventReceiveAnimPlayComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\FlyingComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\FriezeControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\FXControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\GravityComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\LightComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\LinkComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\LinkCurveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\ObjectControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\ObjectControllerComponentUtils.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PatchCurveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PhantomComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PlayAnimBasedOnBoneAngleComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PlayAnimOnEventReceiveComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PlayAnimOnTouchPolylineComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PlayAnimOnWeightChangeComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\PrefetchComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\RelayEventComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\SaveNotificationComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\ShapeComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\SubsceneControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\SwarmComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\SynchronizedAnimComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\TeleporterComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\TODOComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\ToggleAnimOnEventComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\Trail3DComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\TrailComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\VirtualLinkComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Misc\WindComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Player\PlayerControllerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Player\PlayerControllerState.h" />
    <ClInclude Include="..\src\gameplay\Components\Trajectory\TrajectoryData.h" />
    <ClInclude Include="..\src\gameplay\Components\Trajectory\TrajectoryFollowerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trajectory\TrajectoryNodeComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trajectory\TrajectorySegment.h" />
    <ClInclude Include="..\src\gameplay\Components\Trajectory\TrajectorySpawnerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\AnimMarkerTriggerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\CameraDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\DeathDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\DetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\FriezeContactDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\PhantomDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\PhantomTriggerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\PlayerDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\SequenceLauncherComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\ShapeDetectorComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\TriggerComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\Trigger\TriggerOnbuttonPressedComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\InGameTextComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\SimpleTextComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\TutorialTextComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIButtonComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIMenu.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIMenuItemComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIMenuItemText.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UIMenuPageComponent.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UITextBox.h" />
    <ClInclude Include="..\src\gameplay\Components\UI\UITextBoxesComponent.h" />
    <ClInclude Include="..\src\gameplay\GameplayEvents.h" />
    <ClInclude Include="..\src\gameplay\GameplayFactoryFiller.h" />
    <ClInclude Include="..\src\gameplay\GameplayStims.h" />
    <ClInclude Include="..\src\gameplay\GameplayTypes.h" />
    <ClInclude Include="..\src\gameplay\Managers\AIManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\CinematicManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\FactionManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\GameManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\GameModeController.h" />
    <ClInclude Include="..\src\gameplay\Managers\GameScreens\GameScreenBase.h" />
    <ClInclude Include="..\src\gameplay\Managers\GameScreens\GameScreen_Initial.h" />
    <ClInclude Include="..\src\gameplay\Managers\LinkManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\PadRumbleManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\PersistentGameData.h" />
    <ClInclude Include="..\src\gameplay\Managers\RegionsManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\SpawnManagerComponent.h" />
    <ClInclude Include="..\src\gameplay\Managers\StimsManager.h" />
    <ClInclude Include="..\src\gameplay\Managers\WaypointsManager.h" />
    <ClInclude Include="..\src\gameplay\precompiled_gameplay.h" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="..\src\gameplay\RO_Gameplay.natvis" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\gameplay\AI\Actions\AIAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIBezierAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIBounceAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIBounceToLayerAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIBumperAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIDestroyAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIDisableAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIFadeAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIFallAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIFallNoPhysAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIFollowActorAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIFollowCollideActorAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIIdleAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIJumpAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIJumpAngleAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIJumpInDirAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIJumpToTargetAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIPerformHitAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIPlayAnimAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIPlayFXAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIReceiveHitAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AISendEventAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AISpawnAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIStickBoneAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIUTurnAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\AIWalkInDirAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsApexAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Actions\Ballistics\AIBallisticsFixedGravityAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIDeathBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIPlayActionsBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIReceiveHitBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIReplicateParentAnimBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AIRoamingBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Behaviors\AISimplePlayAnimBehavior.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BehaviorTree.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\Blackboard.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTAction.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionCopyFact.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionJumpToBack.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionJumpToTarget.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionPlayAnim.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionRemoveFact.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionSendEventToActor.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionSetFact.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionStayIdle.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionStayIdleLookAt.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTActionWalkToTarget.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDecider.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDeciderFactEqual.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDeciderHasActorsAlive.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDeciderHasFact.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDeciderHasPlayerBehind.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDeciderHasPlayerNear.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTDelay.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTNode.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTSequence.cpp" />
    <ClCompile Include="..\src\gameplay\AI\BTs\BTTimer.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Utils\AIUtils.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Utils\AnimationAtlas.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Utils\EventActivateHandler.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Utils\EventDelayHandler.cpp" />
    <ClCompile Include="..\src\gameplay\AI\Utils\TrajectoryProvider.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\AdditionalBehaviorsComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\AIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\BaseAIControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\BTAIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\GenericAIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\GroundAIControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\SimpleAIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\TimedSpawnerAIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\AI\WaypointComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\ActorSpawnComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\BounceOnPolylinePhysComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\CharacterDebuggerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\ComponentsUtils.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\ConstantMovementComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\DeformOnTrajectoryComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\ParticlePhysComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\PoolActorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\StickToPolylinePhysComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\TimedSpawnerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\TweenComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\TweenInstructions.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Common\TweenInterpreter.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Display\RenderBezierPatchCurveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Display\RenderSimpleAnimComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\AxisPolylineComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\HingePlatformComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\PendulumComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\PointsCollisionComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\PolylineComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\ProceduralPolyline.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\ProceduralSoftPlatformComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\RotatingPolylineComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\SoftPlatform.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\SoftPlatformComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\SolidPolylineComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Environment\SwingSoftPlatform.cpp" />
    <ClCompile Include="..\src\gameplay\Components\FX\BoxInterpolatorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\FX\CircleInterpolatorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\FX\InterpolatorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\FX\SpeedInputProviderComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\AABBPrefetchComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\AfterFxControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\AlwaysActiveActorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\AnimTriggeredComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\ArenaDoorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\BezierCurveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\CameraSubjectComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\CheckpointComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\CurveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\CurveFollowerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\DataErrorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\EventReceiveAnimPlayComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\FlyingComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\FriezeControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\FXControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\GravityComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\LightComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\LinkComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\LinkCurveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\ObjectControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\ObjectControllerComponentUtils.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PatchCurveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PhantomComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PlayAnimBasedOnBoneAngleComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PlayAnimOnEventReceiveComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PlayAnimOnTouchPolylineComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PlayAnimOnWeightChangeComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\PrefetchComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\RelayEventComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\SaveNotificationComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\ShapeComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\SubsceneControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\SwarmComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\SynchronizedAnimComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\TeleporterComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\TODOComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\ToggleAnimOnEventComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\Trail3DComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\TrailComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\VirtualLinkComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Misc\WindComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Player\PlayerControllerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Player\PlayerControllerState.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trajectory\TrajectoryData.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trajectory\TrajectoryFollowerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trajectory\TrajectoryNodeComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trajectory\TrajectorySegment.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trajectory\TrajectorySpawnerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\AnimMarkerTriggerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\CameraDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\DeathDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\DetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\FriezeContactDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\PhantomDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\PhantomTriggerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\PlayerDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\SequenceLauncherComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\ShapeDetectorComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\TriggerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\Trigger\TriggerOnbuttonPressedComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\InGameTextComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\SimpleTextComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\TutorialTextComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIButtonComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIMenu.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIMenuItemComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIMenuItemText.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UIMenuPageComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UITextBox.cpp" />
    <ClCompile Include="..\src\gameplay\Components\UI\UITextBoxesComponent.cpp" />
    <ClCompile Include="..\src\gameplay\GameplayEvents.cpp" />
    <ClCompile Include="..\src\gameplay\GameplayFactoryFiller.cpp" />
    <ClCompile Include="..\src\gameplay\GameplayStims.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\AIManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\CinematicManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\FactionManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\GameManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\GameModeController.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\GameScreens\GameScreenBase.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\GameScreens\GameScreen_Initial.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\LinkManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\PadRumbleManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\PersistentGameData.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\RegionsManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\SpawnManagerComponent.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\StimsManager.cpp" />
    <ClCompile Include="..\src\gameplay\Managers\WaypointsManager.cpp" />
    <ClCompile Include="..\src\gameplay\precompiled_gameplay.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Final|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
