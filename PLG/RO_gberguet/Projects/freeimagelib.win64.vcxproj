<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Final|x64">
      <Configuration>Final</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Retail|x64">
      <Configuration>Retail</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FE451320-D083-4F16-1447-59E9A4EE7DC4}</ProjectGuid>
    <DefaultLanguage>en-US</DefaultLanguage>
    <RootNamespace>FreeImageLib</RootNamespace>
    <ProjectName>FreeImageLib</ProjectName>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <MSBuildExtensionsPath>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\</MSBuildExtensionsPath>
    <VCInstallDir_170>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\VC\</VCInstallDir_170>
    <VCTargetsPath17>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\Microsoft\VC\v170\</VCTargetsPath17>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(Platform)'=='x64'">
    <AdditionalVCTargetsPath>e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\msbuild\vs2022\</AdditionalVCTargetsPath>
    <_VCTargetsPathForToolset>$(AdditionalVCTargetsPath)</_VCTargetsPathForToolset>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <UCRTContentRoot>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UCRTContentRoot>
    <UniversalCRTSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UniversalCRTSdkDir_10>
    <WindowsSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</WindowsSdkDir_10>
    <WindowsSdkDir>$(WindowsSdkDir_10)</WindowsSdkDir>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>freeimagelib</TargetName>
    <OutDir>lib\win64\debug\</OutDir>
    <IntDir>tmp\win64\freeimagelib\debug_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\debug\freeimagelib.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <TargetName>freeimagelib</TargetName>
    <OutDir>lib\win64\final\</OutDir>
    <IntDir>tmp\win64\freeimagelib\final_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\final\freeimagelib.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>freeimagelib</TargetName>
    <OutDir>lib\win64\release\</OutDir>
    <IntDir>tmp\win64\freeimagelib\release_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\release\freeimagelib.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <TargetName>freeimagelib</TargetName>
    <OutDir>lib\win64\retail\</OutDir>
    <IntDir>tmp\win64\freeimagelib\retail_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\retail\freeimagelib.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>FREEIMAGE_LIB;ITF_DEBUG;ITF_FREEIMAGE;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NOMINMAX;NTDDI_VERSION=0x0A000000;OPJ_STATIC;WIN64;_CRT_SECURE_NO_WARNINGS;_DEBUG;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\freeimage\source;..\extern\freeimage\source\deprecationmanager;..\extern\freeimage\source\openexr;..\extern\freeimage\source\openexr\half;..\extern\freeimage\source\openexr\iex;..\extern\freeimage\source\openexr\ilmimf;..\extern\freeimage\source\openexr\ilmthread;..\extern\freeimage\source\openexr\imath;..\extern\zlib;..\src;..\src\core;..\src\core\system</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4267;4291;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\freeimagelib\debug_vs2022\freeimagelib_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <OptimizeReferences>false</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\debug\freeimagelib.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>FREEIMAGE_LIB;ITF_FINAL;ITF_FREEIMAGE;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;OPJ_STATIC;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\freeimage\source;..\extern\freeimage\source\deprecationmanager;..\extern\freeimage\source\openexr;..\extern\freeimage\source\openexr\half;..\extern\freeimage\source\openexr\iex;..\extern\freeimage\source\openexr\ilmimf;..\extern\freeimage\source\openexr\ilmthread;..\extern\freeimage\source\openexr\imath;..\extern\zlib;..\src;..\src\core;..\src\core\system</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4267;4291;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\freeimagelib\final_vs2022\freeimagelib_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\final\freeimagelib.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>FREEIMAGE_LIB;ITF_FREEIMAGE;ITF_MICROSOFT;ITF_RELEASE;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;OPJ_STATIC;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\freeimage\source;..\extern\freeimage\source\deprecationmanager;..\extern\freeimage\source\openexr;..\extern\freeimage\source\openexr\half;..\extern\freeimage\source\openexr\iex;..\extern\freeimage\source\openexr\ilmimf;..\extern\freeimage\source\openexr\ilmthread;..\extern\freeimage\source\openexr\imath;..\extern\zlib;..\src;..\src\core;..\src\core\system</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4267;4291;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\freeimagelib\release_vs2022\freeimagelib_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\release\freeimagelib.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>FREEIMAGE_LIB;ITF_FINAL;ITF_FREEIMAGE;ITF_MICROSOFT;ITF_RETAIL;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;OPJ_STATIC;RETAIL;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\freeimage\source;..\extern\freeimage\source\deprecationmanager;..\extern\freeimage\source\openexr;..\extern\freeimage\source\openexr\half;..\extern\freeimage\source\openexr\iex;..\extern\freeimage\source\openexr\ilmimf;..\extern\freeimage\source\openexr\ilmthread;..\extern\freeimage\source\openexr\imath;..\extern\zlib;..\src;..\src\core;..\src\core\system</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4267;4291;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\freeimagelib\retail_vs2022\freeimagelib_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\retail\freeimagelib.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\extern\FreeImage\Source\CacheFile.h" />
    <ClInclude Include="..\extern\FreeImage\Source\DeprecationManager\DeprecationMgr.h" />
    <ClInclude Include="..\extern\FreeImage\Source\FreeImage.h" />
    <ClInclude Include="..\extern\FreeImage\Source\FreeImageIO.h" />
    <ClInclude Include="..\extern\FreeImage\Source\FreeImageToolkit\Filters.h" />
    <ClInclude Include="..\extern\FreeImage\Source\FreeImageToolkit\Resize.h" />
    <ClInclude Include="..\extern\FreeImage\Source\FreeImage\PSDParser.h" />
    <ClInclude Include="..\extern\FreeImage\Source\Metadata\FIRational.h" />
    <ClInclude Include="..\extern\FreeImage\Source\Metadata\FreeImageTag.h" />
    <ClInclude Include="..\extern\FreeImage\Source\Plugin.h" />
    <ClInclude Include="..\extern\FreeImage\Source\Quantizers.h" />
    <ClInclude Include="..\extern\FreeImage\Source\ToneMapping.h" />
    <ClInclude Include="..\extern\FreeImage\Source\Utilities.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\extern\FreeImage\Source\DeprecationManager\Deprecated.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\DeprecationManager\DeprecationMgr.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Background.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\BSplineRotate.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Channels.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\ClassicRotate.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Colors.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\CopyPaste.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Display.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Flip.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\JPEGTransform.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\MultigridPoissonSolver.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Rescale.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImageToolkit\Resize.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\BitmapAccess.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\CacheFile.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\ColorLookup.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion16_555.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion16_565.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion24.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion32.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion4.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Conversion8.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\ConversionRGBF.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\ConversionType.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\FreeImage.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\FreeImageC.c" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\FreeImageIO.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\GetType.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Halftoning.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\J2KHelper.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\MemoryIO.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\MultiPage.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\NNQuantizer.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PixelAccess.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\Plugin.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginBMP.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginCUT.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginDDS.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginEXR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginG3.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginGIF.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginHDR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginICO.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginIFF.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginJ2K.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginJP2.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginJPEG.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginKOALA.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginMNG.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPCD.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPCX.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPFM.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPICT.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPNG.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPNM.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginPSD.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginRAS.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginRAW.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginSGI.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginTARGA.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginTIFF.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginWBMP.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginXBM.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PluginXPM.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\PSDParser.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\TIFFLogLuv.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\tmoColorConvert.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\tmoDrago03.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\tmoFattal02.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\tmoReinhard05.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\ToneMapping.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\FreeImage\WuQuantizer.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\Exif.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\FIRational.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\FreeImageTag.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\IPTC.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\TagConversion.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\TagLib.cpp" />
    <ClCompile Include="..\extern\FreeImage\Source\Metadata\XTIFF.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
