<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Final|x64">
      <Configuration>Final</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Retail|x64">
      <Configuration>Retail</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C8C000B2-55E4-D4C7-B39A-ACCCF8AA5EFD}</ProjectGuid>
    <DefaultLanguage>en-US</DefaultLanguage>
    <RootNamespace>ITF_Core</RootNamespace>
    <ProjectName>ITF_Core</ProjectName>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <MSBuildExtensionsPath>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\</MSBuildExtensionsPath>
    <VCInstallDir_170>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\VC\</VCInstallDir_170>
    <VCTargetsPath17>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\Microsoft\VC\v170\</VCTargetsPath17>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(Platform)'=='x64'">
    <AdditionalVCTargetsPath>e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\msbuild\vs2022\</AdditionalVCTargetsPath>
    <_VCTargetsPathForToolset>$(AdditionalVCTargetsPath)</_VCTargetsPathForToolset>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <UCRTContentRoot>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UCRTContentRoot>
    <UniversalCRTSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UniversalCRTSdkDir_10>
    <WindowsSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</WindowsSdkDir_10>
    <WindowsSdkDir>$(WindowsSdkDir_10)</WindowsSdkDir>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>itf_core</TargetName>
    <OutDir>lib\win64\debug\</OutDir>
    <IntDir>tmp\win64\itf_core\debug_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\debug\itf_core.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <TargetName>itf_core</TargetName>
    <OutDir>lib\win64\final\</OutDir>
    <IntDir>tmp\win64\itf_core\final_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\final\itf_core.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>itf_core</TargetName>
    <OutDir>lib\win64\release\</OutDir>
    <IntDir>tmp\win64\itf_core\release_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\release\itf_core.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <TargetName>itf_core</TargetName>
    <OutDir>lib\win64\retail\</OutDir>
    <IntDir>tmp\win64\itf_core\retail_vs2022\</IntDir>
    <TargetExt>.lib</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>lib\win64\retail\itf_core.lib</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_DEBUG;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_DEBUG;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\lua\src;..\extern\pugixml\src;..\src;..\src\core</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_core.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_core\debug_vs2022\ITF_Core.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_core\debug_vs2022\itf_core_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <OptimizeReferences>false</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\debug\itf_core.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\lua\src;..\extern\pugixml\src;..\src;..\src\core</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_core.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_core\final_vs2022\ITF_Core.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_core\final_vs2022\itf_core_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\final\itf_core.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>ITF_MICROSOFT;ITF_RELEASE;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\lua\src;..\extern\pugixml\src;..\src;..\src\core</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_core.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_core\release_vs2022\ITF_Core.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_core\release_vs2022\itf_core_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\release\itf_core.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>ITF_FINAL;ITF_MICROSOFT;ITF_RETAIL;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;RETAIL;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_LIB;_WIN32_WINNT=0x0A00;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern;..\extern\lua\src;..\extern\pugixml\src;..\src;..\src\core</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <PrecompiledHeaderFile>precompiled_core.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>tmp\win64\itf_core\retail_vs2022\ITF_Core.pch</PrecompiledHeaderOutputFile>
      <ProgramDatabaseFileName>tmp\win64\itf_core\retail_vs2022\itf_core_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <TargetMachine>MachineX64</TargetMachine>
      <SubSystem/>
      <LinkTimeCodeGeneration>false</LinkTimeCodeGeneration>
      <OutputFile>lib\win64\retail\itf_core.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\src\core\AdaptersInterfaces\FileCacheManager.h" />
    <ClInclude Include="..\src\core\AdaptersInterfaces\FileManager.h" />
    <ClInclude Include="..\src\core\AdaptersInterfaces\SystemAdapter.h" />
    <ClInclude Include="..\src\core\AdaptersInterfaces\ThreadManager.h" />
    <ClInclude Include="..\src\core\Archive.h" />
    <ClInclude Include="..\src\core\ArchiveCounter.h" />
    <ClInclude Include="..\src\core\ArchiveLinker.h" />
    <ClInclude Include="..\src\core\ArchiveMemory.h" />
    <ClInclude Include="..\src\core\ArchiveMemoryWriter.h" />
    <ClInclude Include="..\src\core\AssertManager.h" />
    <ClInclude Include="..\src\core\BaseObject.h" />
    <ClInclude Include="..\src\core\Blob.h" />
    <ClInclude Include="..\src\core\blobUtils.h" />
    <ClInclude Include="..\src\core\boundingvolume\AABB.h" />
    <ClInclude Include="..\src\core\ByteEndian.h" />
    <ClInclude Include="..\src\core\Color.h" />
    <ClInclude Include="..\src\core\ColorInteger.h" />
    <ClInclude Include="..\src\core\Color_Macros.h" />
    <ClInclude Include="..\src\core\Config.h" />
    <ClInclude Include="..\src\core\container\extendedList.h" />
    <ClInclude Include="..\src\core\container\extendedMap.h" />
    <ClInclude Include="..\src\core\container\extendedVector.h" />
    <ClInclude Include="..\src\core\container\FastArray.h" />
    <ClInclude Include="..\src\core\container\FixedArray.h" />
    <ClInclude Include="..\src\core\container\Hash.h" />
    <ClInclude Include="..\src\core\container\KeyArray.h" />
    <ClInclude Include="..\src\core\container\SafeArray.h" />
    <ClInclude Include="..\src\core\crc.h" />
    <ClInclude Include="..\src\core\CTRSpecificConstants.h" />
    <ClInclude Include="..\src\core\DefaultSpecificConstants.h" />
    <ClInclude Include="..\src\core\developer_prefs.h" />
    <ClInclude Include="..\src\core\EngineCommon.h" />
    <ClInclude Include="..\src\core\error\ErrorHandler.h" />
    <ClInclude Include="..\src\core\file\CTR\File_CTR.h" />
    <ClInclude Include="..\src\core\file\Directory.h" />
    <ClInclude Include="..\src\core\file\File.h" />
    <ClInclude Include="..\src\core\file\FilePath.h" />
    <ClInclude Include="..\src\core\file\FileRemote.h" />
    <ClInclude Include="..\src\core\file\FileServer.h" />
    <ClInclude Include="..\src\core\file\iPad\File_iPad.h" />
    <ClInclude Include="..\src\core\file\Nintendo\File_Nintendo.h" />
    <ClInclude Include="..\src\core\file\Path.h" />
    <ClInclude Include="..\src\core\file\PS3\File_ps3.h" />
    <ClInclude Include="..\src\core\file\PS5\File_ps5.h" />
    <ClInclude Include="..\src\core\file\WII\File_WII.h" />
    <ClInclude Include="..\src\core\file\WIN\File_win.h" />
    <ClInclude Include="..\src\core\file\X360\File_X360.h" />
    <ClInclude Include="..\src\core\FlexibleValue.h" />
    <ClInclude Include="..\src\core\IDServer.h" />
    <ClInclude Include="..\src\core\itfstring.h" />
    <ClInclude Include="..\src\core\LocalisationId.h" />
    <ClInclude Include="..\src\core\Macros.h" />
    <ClInclude Include="..\src\core\math\Angle.h" />
    <ClInclude Include="..\src\core\math\BitTweak.h" />
    <ClInclude Include="..\src\core\math\Mathf32.h" />
    <ClInclude Include="..\src\core\math\MathTools.h" />
    <ClInclude Include="..\src\core\math\matrix44\matrix44.h" />
    <ClInclude Include="..\src\core\math\matrix44\matrix44_Software\Matrix44_Software.h" />
    <ClInclude Include="..\src\core\math\matrix44\matrix44_Software\Matrix44_Software_Header.h" />
    <ClInclude Include="..\src\core\math\matrix44\matrix44_x86\Matrix44_x86.h" />
    <ClInclude Include="..\src\core\math\matrix44\matrix44_x86\Matrix44_x86_header.h" />
    <ClInclude Include="..\src\core\math\Ngon2d.h" />
    <ClInclude Include="..\src\core\math\PerlinNoise.h" />
    <ClInclude Include="..\src\core\math\plane.h" />
    <ClInclude Include="..\src\core\math\PS3\FastTrigo_PS3.h" />
    <ClInclude Include="..\src\core\math\quaternion.h" />
    <ClInclude Include="..\src\core\math\spline.h" />
    <ClInclude Include="..\src\core\math\transform.h" />
    <ClInclude Include="..\src\core\math\Triangulate.h" />
    <ClInclude Include="..\src\core\math\vec2d.h" />
    <ClInclude Include="..\src\core\math\vec3d.h" />
    <ClInclude Include="..\src\core\math\WII\MTH_int_WII.h" />
    <ClInclude Include="..\src\core\math\WII\SIMD_Float2.h" />
    <ClInclude Include="..\src\core\memory\allocator\aligned.h" />
    <ClInclude Include="..\src\core\memory\allocator\allocator.h" />
    <ClInclude Include="..\src\core\memory\allocator\allocator_consts.h" />
    <ClInclude Include="..\src\core\memory\allocator\allocator_macros.h" />
    <ClInclude Include="..\src\core\memory\allocator\heap.h" />
    <ClInclude Include="..\src\core\memory\allocator\page.h" />
    <ClInclude Include="..\src\core\memory\allocator\pool.h" />
    <ClInclude Include="..\src\core\memory\IcePairManager.h" />
    <ClInclude Include="..\src\core\memory\mapMemoryTracker.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMng.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngBigAlloc.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngBuckets.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngBucketsPlatform.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngBucketsPrivate.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngConfig.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPage.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPage_Nintendo.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPage_PS5.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPrivate.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPrivatePlatform_Nintendo.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngPrivatePlatform_PS5.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngStdAlloc.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngTracer.h" />
    <ClInclude Include="..\src\core\memory\MemMng\MemMngTracerPlatform.h" />
    <ClInclude Include="..\src\core\memory\memory.h" />
    <ClInclude Include="..\src\core\memory\MemoryDebugger.h" />
    <ClInclude Include="..\src\core\memory\memoryEvent.h" />
    <ClInclude Include="..\src\core\memory\memoryId.h" />
    <ClInclude Include="..\src\core\memory\memoryPoolSized.h" />
    <ClInclude Include="..\src\core\memory\memoryProxy.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyCTR.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyGeneric.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyiPad.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyPS3.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyVITA.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyWII.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyWIN.h" />
    <ClInclude Include="..\src\core\memory\memoryProxyX360.h" />
    <ClInclude Include="..\src\core\memory\memorySettings.h" />
    <ClInclude Include="..\src\core\memory\memoryStatsManager.h" />
    <ClInclude Include="..\src\core\memory\memoryTrackingVector.h" />
    <ClInclude Include="..\src\core\memory\memory_Align.inl" />
    <ClInclude Include="..\src\core\memory\memory_CTR.inl" />
    <ClInclude Include="..\src\core\memory\memory_iPad.inl" />
    <ClInclude Include="..\src\core\memory\memory_Nintendo.h" />
    <ClInclude Include="..\src\core\memory\memory_PS3.h" />
    <ClInclude Include="..\src\core\memory\memory_PS5.h" />
    <ClInclude Include="..\src\core\memory\memory_VITA.inl" />
    <ClInclude Include="..\src\core\memory\memory_WII.h" />
    <ClInclude Include="..\src\core\memory\memory_Win.inl" />
    <ClInclude Include="..\src\core\memory\memory_x360.h" />
    <ClInclude Include="..\src\core\memory\memory_x360.inl" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngBigAlloc_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngBucketsPlatform_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngBucketsPrivate_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngBuckets_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngConfig_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngPage_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngPrivatePlatform_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngPrivate_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngStdAlloc_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngTracerPlatform_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMngTracer_PS3.h" />
    <ClInclude Include="..\src\core\memory\PS3\MemMng_PS3.h" />
    <ClInclude Include="..\src\core\memory\slotallocator.h" />
    <ClInclude Include="..\src\core\memory\slotallocatorManager.h" />
    <ClInclude Include="..\src\core\memory\SSOBuffer.h" />
    <ClInclude Include="..\src\core\memory\STDAllocatorOnITFMemory.h" />
    <ClInclude Include="..\src\core\memory\STDAllocatorOnMalloc.h" />
    <ClInclude Include="..\src\core\memory\threadAllocatorStacker.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_consts_WII.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_heap_WII.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_log_WII.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_macros_WII.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_manager_WII.h" />
    <ClInclude Include="..\src\core\memory\WII\MEM_structs_WII.h" />
    <ClInclude Include="..\src\core\ObjectIDServer.h" />
    <ClInclude Include="..\src\core\ObjectPath.h" />
    <ClInclude Include="..\src\core\OutputDebugger.h" />
    <ClInclude Include="..\src\core\precompiled_core.h" />
    <ClInclude Include="..\src\core\script\LuaBinderFunctions_Math.h" />
    <ClInclude Include="..\src\core\script\LUAHandler.h" />
    <ClInclude Include="..\src\core\Seeder.h" />
    <ClInclude Include="..\src\core\serializer\ObjectFactory.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializer.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerDep.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerFile.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerMem.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerMemoryDump.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObject.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectBinary.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectBinaryFile.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectBinaryMem.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectLua.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectParser.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerObjectSignature.h" />
    <ClInclude Include="..\src\core\serializer\ZSerializerToolXML.h" />
    <ClInclude Include="..\src\core\sound\volume.h" />
    <ClInclude Include="..\src\core\StackWalker.h" />
    <ClInclude Include="..\src\core\String8.h" />
    <ClInclude Include="..\src\core\StringID.h" />
    <ClInclude Include="..\src\core\system\AutoLock.h" />
    <ClInclude Include="..\src\core\system\CTR\Synchronize_CTR.h" />
    <ClInclude Include="..\src\core\system\CTR\Thread_CTR.h" />
    <ClInclude Include="..\src\core\system\iPad\Synchronize_iPad.h" />
    <ClInclude Include="..\src\core\system\iPad\Thread_iPad.h" />
    <ClInclude Include="..\src\core\system\Nintendo\Atomic_Nintendo.h" />
    <ClInclude Include="..\src\core\system\Nintendo\Synchronize_Nintendo.h" />
    <ClInclude Include="..\src\core\system\Nintendo\Thread_Nintendo.h" />
    <ClInclude Include="..\src\core\system\ProcessSpawner.h" />
    <ClInclude Include="..\src\core\system\PS3\ModuleManager_ps3.h" />
    <ClInclude Include="..\src\core\system\PS3\Purs\PursJob_PS3.h" />
    <ClInclude Include="..\src\core\system\PS3\Purs\PursUtil_PS3.h" />
    <ClInclude Include="..\src\core\system\PS3\Purs\Purs_PS3.h" />
    <ClInclude Include="..\src\core\system\PS3\Synchronize_ps3.h" />
    <ClInclude Include="..\src\core\system\PS3\Thread_ps3.h" />
    <ClInclude Include="..\src\core\system\PS5\Synchronize_ps5.h" />
    <ClInclude Include="..\src\core\system\PS5\SystemModuleManager_ps5.h" />
    <ClInclude Include="..\src\core\system\PS5\Thread_ps5.h" />
    <ClInclude Include="..\src\core\system\Synchronize.h" />
    <ClInclude Include="..\src\core\system\SystemModuleManager.h" />
    <ClInclude Include="..\src\core\system\Thread.h" />
    <ClInclude Include="..\src\core\system\VITA\Synchronize_VITA.h" />
    <ClInclude Include="..\src\core\system\VITA\Thread_VITA.h" />
    <ClInclude Include="..\src\core\system\WII\Synchronize_WII.h" />
    <ClInclude Include="..\src\core\system\WII\Thread_WII.h" />
    <ClInclude Include="..\src\core\system\WIN\Synchronize_win.h" />
    <ClInclude Include="..\src\core\system\WIN\Thread_win.h" />
    <ClInclude Include="..\src\core\system\X360\Synchronize_x360.h" />
    <ClInclude Include="..\src\core\system\X360\Thread_x360.h" />
    <ClInclude Include="..\src\core\system\X360\XGD3Check_x360.h" />
    <ClInclude Include="..\src\core\templateSingleton.h" />
    <ClInclude Include="..\src\core\types.h" />
    <ClInclude Include="..\src\core\UnicodeTools.h" />
    <ClInclude Include="..\src\core\utility.h" />
    <ClInclude Include="..\src\core\versioning.h" />
    <ClInclude Include="..\src\core\VITASpecificConstants.h" />
    <ClInclude Include="..\src\core\vldManager.h" />
    <ClInclude Include="..\src\core\WiiSpecificConstants.h" />
    <ClInclude Include="..\src\core\XML\PugiXMLWrap.h" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="..\src\core\RO_UAF_Core.natvis" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\core\AdaptersInterfaces\FileCacheManager.cpp" />
    <ClCompile Include="..\src\core\AdaptersInterfaces\SystemAdapter.cpp" />
    <ClCompile Include="..\src\core\Archive.cpp" />
    <ClCompile Include="..\src\core\ArchiveLinker.cpp" />
    <ClCompile Include="..\src\core\AssertManager.cpp" />
    <ClCompile Include="..\src\core\BaseObject.cpp" />
    <ClCompile Include="..\src\core\Blob.cpp" />
    <ClCompile Include="..\src\core\blobUtils.cpp" />
    <ClCompile Include="..\src\core\boundingvolume\AABB.cpp" />
    <ClCompile Include="..\src\core\ByteEndian.cpp" />
    <ClCompile Include="..\src\core\Color.cpp" />
    <ClCompile Include="..\src\core\ColorInteger.cpp" />
    <ClCompile Include="..\src\core\Config.cpp" />
    <ClCompile Include="..\src\core\container\FixedArray.cpp" />
    <ClCompile Include="..\src\core\container\KeyArray.cpp" />
    <ClCompile Include="..\src\core\container\SafeArray.cpp" />
    <ClCompile Include="..\src\core\crc.cpp" />
    <ClCompile Include="..\src\core\error\ErrorHandler.cpp" />
    <ClCompile Include="..\src\core\file\CTR\Directory_CTR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\CTR\File_CTR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\Directory.cpp" />
    <ClCompile Include="..\src\core\file\File.cpp" />
    <ClCompile Include="..\src\core\file\FilePath.cpp" />
    <ClCompile Include="..\src\core\file\FileRemote.cpp" />
    <ClCompile Include="..\src\core\file\FileServer.cpp" />
    <ClCompile Include="..\src\core\file\iPad\Directory_iPad.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\iPad\File_iPad.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\Nintendo\Directory_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\Nintendo\File_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\Path.cpp" />
    <ClCompile Include="..\src\core\file\PS3\Directory_ps3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\PS3\File_ps3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\PS5\Directory_ps5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\PS5\File_ps5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\WII\Directory_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\WII\File_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\WIN\Directory_win.cpp" />
    <ClCompile Include="..\src\core\file\WIN\File_win.cpp" />
    <ClCompile Include="..\src\core\file\X360\Directory_X360.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\file\X360\File_X360.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\FlexibleValue.cpp" />
    <ClCompile Include="..\src\core\IDServer.cpp" />
    <ClCompile Include="..\src\core\itfstring.cpp" />
    <ClCompile Include="..\src\core\LocalisationId.cpp" />
    <ClCompile Include="..\src\core\math\Angle.cpp" />
    <ClCompile Include="..\src\core\math\MathTools.cpp" />
    <ClCompile Include="..\src\core\math\Ngon2d.cpp" />
    <ClCompile Include="..\src\core\math\PerlinNoise.cpp" />
    <ClCompile Include="..\src\core\math\plane.cpp" />
    <ClCompile Include="..\src\core\math\spline.cpp" />
    <ClCompile Include="..\src\core\math\transform.cpp" />
    <ClCompile Include="..\src\core\math\Triangulate.cpp" />
    <ClCompile Include="..\src\core\math\vec2d.cpp" />
    <ClCompile Include="..\src\core\math\vec3d.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\allocator.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\allocator_init.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\heap_alloc.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\heap_check.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\heap_misc.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\heap_pool.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\page.cpp" />
    <ClCompile Include="..\src\core\memory\allocator\pool.cpp" />
    <ClCompile Include="..\src\core\memory\IcePairManager.cpp" />
    <ClCompile Include="..\src\core\memory\MemMng\MemMng.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngBigAlloc.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngBuckets.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngBucketsDebug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngBucketsPlatform.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngDebug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngDebugPlatform_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngDebugPlatform_PS5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngPage_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngPage_PS5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngStdAlloc.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngTracer.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngTracerPlatform_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\MemMng\MemMngTracerPlatform_PS5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\memory.cpp" />
    <ClCompile Include="..\src\core\memory\MemoryDebugger.cpp" />
    <ClCompile Include="..\src\core\memory\memoryDumpStats.cpp" />
    <ClCompile Include="..\src\core\memory\memoryEvent.cpp" />
    <ClCompile Include="..\src\core\memory\memoryPoolSized.cpp" />
    <ClCompile Include="..\src\core\memory\memorySettings.cpp" />
    <ClCompile Include="..\src\core\memory\memoryStatsManager.cpp" />
    <ClCompile Include="..\src\core\memory\PS3\MemMngBigAlloc_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngBucketsDebug_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngBucketsPlatform_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngBuckets_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngDebugPlatform_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngDebug_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngStdAlloc_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngTracerPlatform_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMngTracer_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\PS3\MemMng_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\slotallocator.cpp" />
    <ClCompile Include="..\src\core\memory\slotallocatorManager.cpp" />
    <ClCompile Include="..\src\core\memory\threadAllocatorStacker.cpp" />
    <ClCompile Include="..\src\core\memory\WII\MEM_heap_alloc_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_heap_check_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_heap_misc_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_heap_pool_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_init_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_log_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\memory\WII\MEM_manager_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\ObjectIDServer.cpp" />
    <ClCompile Include="..\src\core\ObjectPath.cpp" />
    <ClCompile Include="..\src\core\OutputDebugger.cpp" />
    <ClCompile Include="..\src\core\precompiled_core.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Final|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\src\core\script\LuaBinderFunctions_Math.cpp" />
    <ClCompile Include="..\src\core\script\LUAHandler.cpp" />
    <ClCompile Include="..\src\core\serializer\ObjectFactory.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerDep.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerFile.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerMem.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerMemoryDump.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObject.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectBinary.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectBinaryFile.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectBinaryMem.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectLua.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectParser.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerObjectSignature.cpp" />
    <ClCompile Include="..\src\core\serializer\ZSerializerToolXML.cpp" />
    <ClCompile Include="..\src\core\StackWalker.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\String8.cpp" />
    <ClCompile Include="..\src\core\StringID.cpp" />
    <ClCompile Include="..\src\core\String_UnitTest.cpp" />
    <ClCompile Include="..\src\core\system\CTR\Synchronize_CTR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\CTR\Thread_CTR.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\iPad\Synchronize_iPad.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\iPad\Thread_iPad.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\Nintendo\Synchronize_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\Nintendo\Thread_Nintendo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\ProcessSpawner.cpp" />
    <ClCompile Include="..\src\core\system\PS3\ModuleManager_ps3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS3\Purs\PursJob_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS3\Purs\PursUtil_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS3\Purs\Purs_PS3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS3\Synchronize_ps3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS3\Thread_ps3.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS5\Synchronize_ps5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS5\SystemModuleManager_ps5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\PS5\Thread_ps5.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\SystemModuleManager.cpp" />
    <ClCompile Include="..\src\core\system\Thread.cpp" />
    <ClCompile Include="..\src\core\system\VITA\Synchronize_VITA.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\VITA\Thread_VITA.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\WII\Synchronize_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\WII\Thread_WII.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\WIN\Synchronize_win.cpp" />
    <ClCompile Include="..\src\core\system\WIN\Thread_win.cpp" />
    <ClCompile Include="..\src\core\system\X360\Synchronize_x360.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\X360\Thread_x360.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\system\X360\XGD3Check_x360.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Final|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\src\core\types.cpp" />
    <ClCompile Include="..\src\core\UnicodeTools.cpp" />
    <ClCompile Include="..\src\core\versioning.cpp" />
    <ClCompile Include="..\src\core\vldManager.cpp" />
    <ClCompile Include="..\src\core\XML\PugiXMLWrap.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
