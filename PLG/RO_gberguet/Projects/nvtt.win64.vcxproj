<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Final|x64">
      <Configuration>Final</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Retail|x64">
      <Configuration>Retail</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F18CD482-42EF-E0FD-DAAE-04C69FCB5F7F}</ProjectGuid>
    <DefaultLanguage>en-US</DefaultLanguage>
    <RootNamespace>NVTT</RootNamespace>
    <ProjectName>NVTT</ProjectName>
    <DisableInstalledVCTargetsUse>true</DisableInstalledVCTargetsUse>
    <MSBuildExtensionsPath>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\</MSBuildExtensionsPath>
    <VCInstallDir_170>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\VC\</VCInstallDir_170>
    <VCTargetsPath17>c:\users\<USER>\.nuget\packages\vs2022_buildtools\17.12.2\tools\MSBuild\Microsoft\VC\v170\</VCTargetsPath17>
  </PropertyGroup>
  <PropertyGroup Label="Globals" Condition="'$(Platform)'=='x64'">
    <AdditionalVCTargetsPath>e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\msbuild\vs2022\</AdditionalVCTargetsPath>
    <_VCTargetsPathForToolset>$(AdditionalVCTargetsPath)</_VCTargetsPathForToolset>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <UCRTContentRoot>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UCRTContentRoot>
    <UniversalCRTSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</UniversalCRTSdkDir_10>
    <WindowsSdkDir_10>e:\.nuget\dd\win-sdk.10.22621.755\tools\10\</WindowsSdkDir_10>
    <WindowsSdkDir>$(WindowsSdkDir_10)</WindowsSdkDir>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>x64_nvtt_d</TargetName>
    <OutDir>..\bin\</OutDir>
    <IntDir>tmp\win64\nvtt\debug_vs2022\</IntDir>
    <TargetExt>.dll</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>..\bin\x64_nvtt_d.dll</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <TargetName>x64_nvtt_f</TargetName>
    <OutDir>..\bin\</OutDir>
    <IntDir>tmp\win64\nvtt\final_vs2022\</IntDir>
    <TargetExt>.dll</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>..\bin\x64_nvtt_f.dll</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>x64_nvtt_r</TargetName>
    <OutDir>..\bin\</OutDir>
    <IntDir>tmp\win64\nvtt\release_vs2022\</IntDir>
    <TargetExt>.dll</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>..\bin\x64_nvtt_r.dll</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <TargetName>x64_nvtt</TargetName>
    <OutDir>..\bin\</OutDir>
    <IntDir>tmp\win64\nvtt\retail_vs2022\</IntDir>
    <TargetExt>.dll</TargetExt>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <OutputFile>..\bin\x64_nvtt.dll</OutputFile>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>HAVE_CUDA;ITF_DEBUG;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NOMINMAX;NTDDI_VERSION=0x0A000000;NVTT_EXPORTS;NVTT_SHARED;WIN64;_CRT_SECURE_NO_WARNINGS;_DEBUG;_HAS_EXCEPTIONS=0;_USRDLL;_WIN32_WINNT=0x0A00;_WINDOWS;__MMX__;__SSE2__;__SSE__;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\cuda\include;..\extern\nvidia texture tools 2\include;..\extern\nvidia texture tools 2\src\gnuwin32\include;..\extern\nvidia texture tools 2\src\project\vs2012;..\extern\nvidia texture tools 2\src\src</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4244;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\nvtt\debug_vs2022\x64_nvtt_d_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\bin\x64_nvtt_d.dll</OutputFile>
      <ShowProgress>NotSet</ShowProgress>
      <AdditionalLibraryDirectories>..\extern\cuda\lib\x64;lib\win64\debug</AdditionalLibraryDirectories>
      <ProgramDatabaseFile>..\bin\x64_nvtt_d.pdb</ProgramDatabaseFile>
      <GenerateMapFile>false</GenerateMapFile>
      <MapExports>false</MapExports>
      <SwapRunFromCD>false</SwapRunFromCD>
      <SwapRunFromNET>false</SwapRunFromNET>
      <Driver>NotSet</Driver>
      <OptimizeReferences>false</OptimizeReferences>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <ProfileGuidedDatabase>
      </ProfileGuidedDatabase>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <IgnoreEmbeddedIDL>false</IgnoreEmbeddedIDL>
      <TypeLibraryResourceID>1</TypeLibraryResourceID>
      <NoEntryPoint>false</NoEntryPoint>
      <SetChecksum>false</SetChecksum>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <TurnOffAssemblyGeneration>false</TurnOffAssemblyGeneration>
      <TargetMachine>MachineX64</TargetMachine>
      <Profile>false</Profile>
      <CLRImageType>Default</CLRImageType>
      <LinkErrorReporting>PromptImmediately</LinkErrorReporting>
      <AdditionalDependencies>libpng.lib;nvcore.lib;nvimage.lib;nvmath.lib;squish.lib;zlib.lib;cudart.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <IgnoreSpecificDefaultLibraries></IgnoreSpecificDefaultLibraries>
      <LargeAddressAware>true</LargeAddressAware>
      <MapFileName></MapFileName>
      <ImportLibrary>..\bin\x64_nvtt_d.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Final|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>HAVE_CUDA;ITF_FINAL;ITF_MICROSOFT;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;NVTT_EXPORTS;NVTT_SHARED;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_USRDLL;_WIN32_WINNT=0x0A00;_WINDOWS;__MMX__;__SSE2__;__SSE__;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\cuda\include;..\extern\nvidia texture tools 2\include;..\extern\nvidia texture tools 2\src\gnuwin32\include;..\extern\nvidia texture tools 2\src\project\vs2012;..\extern\nvidia texture tools 2\src\src</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4244;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\nvtt\final_vs2022\x64_nvtt_f_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\bin\x64_nvtt_f.dll</OutputFile>
      <ShowProgress>NotSet</ShowProgress>
      <AdditionalLibraryDirectories>..\extern\cuda\lib\x64;lib\win64\final</AdditionalLibraryDirectories>
      <ProgramDatabaseFile>..\bin\x64_nvtt_f.pdb</ProgramDatabaseFile>
      <GenerateMapFile>false</GenerateMapFile>
      <MapExports>false</MapExports>
      <SwapRunFromCD>false</SwapRunFromCD>
      <SwapRunFromNET>false</SwapRunFromNET>
      <Driver>NotSet</Driver>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <ProfileGuidedDatabase>
      </ProfileGuidedDatabase>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <IgnoreEmbeddedIDL>false</IgnoreEmbeddedIDL>
      <TypeLibraryResourceID>1</TypeLibraryResourceID>
      <NoEntryPoint>false</NoEntryPoint>
      <SetChecksum>false</SetChecksum>
      <TurnOffAssemblyGeneration>false</TurnOffAssemblyGeneration>
      <TargetMachine>MachineX64</TargetMachine>
      <Profile>false</Profile>
      <CLRImageType>Default</CLRImageType>
      <LinkErrorReporting>PromptImmediately</LinkErrorReporting>
      <AdditionalDependencies>libpng.lib;nvcore.lib;nvimage.lib;nvmath.lib;squish.lib;zlib.lib;cudart.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <IgnoreSpecificDefaultLibraries></IgnoreSpecificDefaultLibraries>
      <LargeAddressAware>true</LargeAddressAware>
      <MapFileName></MapFileName>
      <ImportLibrary>..\bin\x64_nvtt_f.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>HAVE_CUDA;ITF_MICROSOFT;ITF_RELEASE;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;NVTT_EXPORTS;NVTT_SHARED;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_USRDLL;_WIN32_WINNT=0x0A00;_WINDOWS;__MMX__;__SSE2__;__SSE__;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\cuda\include;..\extern\nvidia texture tools 2\include;..\extern\nvidia texture tools 2\src\gnuwin32\include;..\extern\nvidia texture tools 2\src\project\vs2012;..\extern\nvidia texture tools 2\src\src</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4244;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\nvtt\release_vs2022\x64_nvtt_r_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\bin\x64_nvtt_r.dll</OutputFile>
      <ShowProgress>NotSet</ShowProgress>
      <AdditionalLibraryDirectories>..\extern\cuda\lib\x64;lib\win64\release</AdditionalLibraryDirectories>
      <ProgramDatabaseFile>..\bin\x64_nvtt_r.pdb</ProgramDatabaseFile>
      <GenerateMapFile>false</GenerateMapFile>
      <MapExports>false</MapExports>
      <SwapRunFromCD>false</SwapRunFromCD>
      <SwapRunFromNET>false</SwapRunFromNET>
      <Driver>NotSet</Driver>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <ProfileGuidedDatabase>
      </ProfileGuidedDatabase>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <IgnoreEmbeddedIDL>false</IgnoreEmbeddedIDL>
      <TypeLibraryResourceID>1</TypeLibraryResourceID>
      <NoEntryPoint>false</NoEntryPoint>
      <SetChecksum>false</SetChecksum>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <TurnOffAssemblyGeneration>false</TurnOffAssemblyGeneration>
      <TargetMachine>MachineX64</TargetMachine>
      <Profile>false</Profile>
      <CLRImageType>Default</CLRImageType>
      <LinkErrorReporting>PromptImmediately</LinkErrorReporting>
      <AdditionalDependencies>libpng.lib;nvcore.lib;nvimage.lib;nvmath.lib;squish.lib;zlib.lib;cudart.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <IgnoreSpecificDefaultLibraries></IgnoreSpecificDefaultLibraries>
      <LargeAddressAware>true</LargeAddressAware>
      <MapFileName></MapFileName>
      <ImportLibrary>..\bin\x64_nvtt_r.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>HAVE_CUDA;ITF_FINAL;ITF_MICROSOFT;ITF_RETAIL;ITF_WIN64;ITF_WINDOWS;NDEBUG;NOMINMAX;NTDDI_VERSION=0x0A000000;NVTT_EXPORTS;NVTT_SHARED;RETAIL;WIN64;_CRT_SECURE_NO_WARNINGS;_HAS_EXCEPTIONS=0;_USRDLL;_WIN32_WINNT=0x0A00;_WINDOWS;__MMX__;__SSE2__;__SSE__;%(PreprocessorDefinitions);$(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\extern\cuda\include;..\extern\nvidia texture tools 2\include;..\extern\nvidia texture tools 2\src\gnuwin32\include;..\extern\nvidia texture tools 2\src\project\vs2012;..\extern\nvidia texture tools 2\src\src</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>true</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <UseUnicodeForAssemblerListing>false</UseUnicodeForAssemblerListing>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <FloatingPointModel>Precise</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <OpenMPSupport>false</OpenMPSupport>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <BrowseInformation>false</BrowseInformation>
      <CallingConvention>Cdecl</CallingConvention>
      <CompileAs>Default</CompileAs>
      <DisableSpecificWarnings>4100;4127;4201;4244;4324;4456;5033</DisableSpecificWarnings>
      <AdditionalOptions>/Zc:__cplusplus</AdditionalOptions>
      <ProgramDatabaseFileName>tmp\win64\nvtt\retail_vs2022\x64_nvtt_compiler.pdb</ProgramDatabaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\bin\x64_nvtt.dll</OutputFile>
      <ShowProgress>NotSet</ShowProgress>
      <AdditionalLibraryDirectories>..\extern\cuda\lib\x64;lib\win64\retail</AdditionalLibraryDirectories>
      <ProgramDatabaseFile>..\bin\x64_nvtt.pdb</ProgramDatabaseFile>
      <GenerateMapFile>false</GenerateMapFile>
      <MapExports>false</MapExports>
      <SwapRunFromCD>false</SwapRunFromCD>
      <SwapRunFromNET>false</SwapRunFromNET>
      <Driver>NotSet</Driver>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <ProfileGuidedDatabase>
      </ProfileGuidedDatabase>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <IgnoreEmbeddedIDL>false</IgnoreEmbeddedIDL>
      <TypeLibraryResourceID>1</TypeLibraryResourceID>
      <NoEntryPoint>false</NoEntryPoint>
      <SetChecksum>false</SetChecksum>
      <TurnOffAssemblyGeneration>false</TurnOffAssemblyGeneration>
      <TargetMachine>MachineX64</TargetMachine>
      <Profile>false</Profile>
      <CLRImageType>Default</CLRImageType>
      <LinkErrorReporting>PromptImmediately</LinkErrorReporting>
      <AdditionalDependencies>libpng.lib;nvcore.lib;nvimage.lib;nvmath.lib;squish.lib;zlib.lib;cudart.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <IgnoreSpecificDefaultLibraries></IgnoreSpecificDefaultLibraries>
      <LargeAddressAware>true</LargeAddressAware>
      <MapFileName></MapFileName>
      <ImportLibrary>..\bin\x64_nvtt.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressDXT.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressionOptions.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\Compressor.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressRGB.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\Bitmaps.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\ConvolveKernel.cu" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CudaCompressDXT.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CudaMath.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CudaUtils.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\InputOptions.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\nvtt.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\nvtt_wrapper.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\OptimalCompressDXT.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\OutputOptions.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\QuickCompressDXT.h" />
    <ClInclude Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\SingleColorLookup.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CompressKernel.cu">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Compile cuda script</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"..\extern\cuda\bin\nvcc.exe" -arch=sm_75 -m64 -ccbin "$(VC_ExecutablePath_x64)" -c -D_DEBUG -DWIN32 -D_CONSOLE -D_MBCS -Xcompiler /EHsc,/W3,/nologo,/Od,/Zi,/RTC1,/MDd -I"e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\Cuda\include" -I./ -o "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\Win64\NVTT\Debug_vs2022\CompressKernel.obj" "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CompressKernel.cu"&#x0D;&#x0A;</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\extern\cuda\bin\nvcc.exe;..\extern\nvidia texture tools 2\src\src\nvtt\cuda\cudamath.h</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">tmp\win64\nvtt\debug_vs2022\compresskernel.obj</Outputs>
      <Message Condition="'$(Configuration)|$(Platform)'=='Final|x64'">Compile cuda script</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Final|x64'">"..\extern\cuda\bin\nvcc.exe" -arch=sm_75 -m64 -ccbin "$(VC_ExecutablePath_x64)" -c -DNDEBUG -DWIN32 -D_CONSOLE -D_MBCS -Xcompiler /EHsc,/W3,/nologo,/O2,/Zi,/MD -I"e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\Cuda\include" -I./ -o "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\Win64\NVTT\Final_vs2022\CompressKernel.obj" "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CompressKernel.cu"&#x0D;&#x0A;</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Final|x64'">..\extern\cuda\bin\nvcc.exe;..\extern\nvidia texture tools 2\src\src\nvtt\cuda\cudamath.h</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Final|x64'">tmp\win64\nvtt\final_vs2022\compresskernel.obj</Outputs>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Compile cuda script</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"..\extern\cuda\bin\nvcc.exe" -arch=sm_75 -m64 -ccbin "$(VC_ExecutablePath_x64)" -c -DNDEBUG -DWIN32 -D_CONSOLE -D_MBCS -Xcompiler /EHsc,/W3,/nologo,/O2,/Zi,/MD -I"e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\Cuda\include" -I./ -o "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\Win64\NVTT\Release_vs2022\CompressKernel.obj" "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CompressKernel.cu"&#x0D;&#x0A;</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\extern\cuda\bin\nvcc.exe;..\extern\nvidia texture tools 2\src\src\nvtt\cuda\cudamath.h</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">tmp\win64\nvtt\release_vs2022\compresskernel.obj</Outputs>
      <Message Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">Compile cuda script</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">"..\extern\cuda\bin\nvcc.exe" -arch=sm_75 -m64 -ccbin "$(VC_ExecutablePath_x64)" -c -DNDEBUG -DWIN32 -D_CONSOLE -D_MBCS -Xcompiler /EHsc,/W3,/nologo,/O2,/Zi,/MD -I"e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\Cuda\include" -I./ -o "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\Projects\tmp\Win64\NVTT\Retail_vs2022\CompressKernel.obj" "e:\ro\plg\ro_gberguet\sharpmake_build\scripts\..\..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CompressKernel.cu"&#x0D;&#x0A;</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">..\extern\cuda\bin\nvcc.exe;..\extern\nvidia texture tools 2\src\src\nvtt\cuda\cudamath.h</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Retail|x64'">tmp\win64\nvtt\retail_vs2022\compresskernel.obj</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressDXT.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressionOptions.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\Compressor.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\CompressRGB.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CudaCompressDXT.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\cuda\CudaUtils.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\InputOptions.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\nvtt.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\nvtt_wrapper.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\OptimalCompressDXT.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\OutputOptions.cpp" />
    <ClCompile Include="..\extern\NVIDIA Texture Tools 2\src\src\nvtt\QuickCompressDXT.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ItemGroup>
    <ProjectReference Include="nvmath.win64.vcxproj">
      <Project>{2031D5B5-F99E-2FFC-4CE0-CECA14E3FD1E}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="libpng.win64.vcxproj">
      <Project>{237E579F-E799-F65A-4344-E1B971F78EA1}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="nvcore.win64.vcxproj">
      <Project>{61586470-743B-CB6A-D882-730448A2C28F}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="squish.win64.vcxproj">
      <Project>{A7D9065D-212D-5C59-B9B1-F97F0D4D9E39}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="zlib.win64.vcxproj">
      <Project>{AE81AFAF-A610-AB35-2F9E-42DFA7127991}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <ProjectReference Include="nvimage.win64.vcxproj">
      <Project>{D9135A74-9CE4-502D-19C7-331D812DB4B4}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemGroup>
</Project>
