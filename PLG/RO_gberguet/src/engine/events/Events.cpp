#include "precompiled_engine.h"

#ifndef _ITF_EVENTS_H_
#include "engine/events/Events.h"
#endif //_ITF_EVENTS_H_

namespace ITF
{

IMPLEMENT_OBJECT_RTTI(Event)
IMPLEMENT_OBJECT_RTTI(EventTeleport)
IMPLEMENT_OBJECT_RTTI(EventTeleportToActor)
IMPLEMENT_OBJECT_RTTI(EventTrigger)
IMPLEMENT_OBJECT_RTTI(EventReset)
IMPLEMENT_OBJECT_RTTI(EventDie)
IMPLEMENT_OBJECT_RTTI(EventActivateCheckpoint)
IMPLEMENT_OBJECT_RTTI(EventSequenceControl)
IMPLEMENT_OBJECT_RTTI(EventSequenceActivatePlayers)
IMPLEMENT_OBJECT_RTTI(EventSequenceActorActivate)
IMPLEMENT_OBJECT_RTTI(EventSequenceActorSaveZ)
IMPLEMENT_OBJECT_RTTI(EventSequenceSpeedChange)
IMPLEMENT_OBJECT_RTTI(EventSequenceActorPrepare)
IMPLEMENT_OBJECT_RTTI(EventSequenceActorReady)
IMPLEMENT_OBJECT_RTTI(EventSequenceSetPlayerPos)
IMPLEMENT_OBJECT_RTTI(EventWaitForActor)
IMPLEMENT_OBJECT_RTTI(EventEndWait)
IMPLEMENT_OBJECT_RTTI(EventSkipSequence)
IMPLEMENT_OBJECT_RTTI(EventVideoCapture)
IMPLEMENT_OBJECT_RTTI(EventPause)
IMPLEMENT_OBJECT_RTTI(EventAddForce)
IMPLEMENT_OBJECT_RTTI(EventSetSpeed)
IMPLEMENT_OBJECT_RTTI(EventUnstick)
IMPLEMENT_OBJECT_RTTI(EventChildActorDestroyed)
IMPLEMENT_OBJECT_RTTI(EventAnimChanged)
IMPLEMENT_OBJECT_RTTI(EventSetBusVolume)
IMPLEMENT_OBJECT_RTTI(EventSetBusReverb)
IMPLEMENT_OBJECT_RTTI(EventSetBusFilter)
IMPLEMENT_OBJECT_RTTI(EventRegisterCameraSubject)
IMPLEMENT_OBJECT_RTTI(EventCameraShake)
IMPLEMENT_OBJECT_RTTI(EventCameraIgnoreShake)
IMPLEMENT_OBJECT_RTTI(EventPadRumbleStart)
IMPLEMENT_OBJECT_RTTI(EventPadRumbleStop)
IMPLEMENT_OBJECT_RTTI(EventMetronomeSetBPM)
IMPLEMENT_OBJECT_RTTI(EventSetFloatInput)
IMPLEMENT_OBJECT_RTTI(EventSetUintInput)
IMPLEMENT_OBJECT_RTTI(EventScaleChanged)
IMPLEMENT_OBJECT_RTTI(EventBossBubonHit)
IMPLEMENT_OBJECT_RTTI(EventGeneric)
IMPLEMENT_OBJECT_RTTI(EventOnLink)
IMPLEMENT_OBJECT_RTTI(EventSingletonConfigChanged)
IMPLEMENT_OBJECT_RTTI(EventUndelaySpawn)
IMPLEMENT_OBJECT_RTTI(EventFluidCollisionPolylineChanged)
IMPLEMENT_OBJECT_RTTI(EventPlayMusic)
IMPLEMENT_OBJECT_RTTI(EventStopMusic)
IMPLEMENT_OBJECT_RTTI(EventResetAfterFxAlpha)
IMPLEMENT_OBJECT_RTTI(EventOnLinkedToWaveGenerator)
IMPLEMENT_OBJECT_RTTI(EventShowScoreboard)
IMPLEMENT_OBJECT_RTTI(EventGoToMainMenu)
IMPLEMENT_OBJECT_RTTI(EventBusMix)
IMPLEMENT_OBJECT_RTTI(EventPlayMovie)

BEGIN_SERIALIZATION(Event)
    SERIALIZE_MEMBER("sender",m_sender);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventTrigger)
    SERIALIZE_MEMBER("activated",m_activated);
    SERIALIZE_MEMBER("activator",m_activator);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventTeleport)
    SERIALIZE_MEMBER("applyPosAndAngle",m_applyPosAndAngle);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventReset)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventDie)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventActivateCheckpoint)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceControl)
    SERIALIZE_ENUM_BEGIN("state",m_state);
        SERIALIZE_ENUM_VAR(SequencePlayerComponent::State_Stopped);
        SERIALIZE_ENUM_VAR(SequencePlayerComponent::State_Playing);
        SERIALIZE_ENUM_VAR(SequencePlayerComponent::State_Paused);
    SERIALIZE_ENUM_END();
    SERIALIZE_MEMBER("label",m_label);
    SERIALIZE_MEMBER("forceLabel",m_forceLabel);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceActorActivate)
    SERIALIZE_MEMBER("activate",m_activate);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceActivatePlayers)
    SERIALIZE_MEMBER("activate",m_activate);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceActorSaveZ)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceSetPlayerPos)
    SERIALIZE_MEMBER("actor",m_actor);
    SERIALIZE_MEMBER("playerId",m_playerId);
    SERIALIZE_MEMBER("playerMode",m_playerMode);
    SERIALIZE_MEMBER("useBaseAdjust",m_useBaseAdjust);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceSpeedChange)
    SERIALIZE_MEMBER("speed",m_speed);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceActorPrepare)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSequenceActorReady)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventWaitForActor)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventEndWait)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSkipSequence)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventVideoCapture)
SERIALIZE_MEMBER("start",m_start);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventPause)
    SERIALIZE_MEMBER("pause",m_pause);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventAddForce)
    SERIALIZE_MEMBER("force",m_force);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSetSpeed)
    SERIALIZE_MEMBER("speed",m_speed);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventUnstick)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventChildActorDestroyed)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventAnimChanged)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSetBusVolume)
    SERIALIZE_MEMBER("bus",m_bus);
    SERIALIZE_MEMBER("volume",m_volume);
    SERIALIZE_MEMBER("time", m_time);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSetBusReverb)
    SERIALIZE_MEMBER("bus",m_bus);
    SERIALIZE_MEMBER("changeActivation",m_changeActivation);
    SERIALIZE_MEMBER("activate",m_activate);
    SERIALIZE_MEMBER("changePreset",m_changePreset);
    SERIALIZE_ENUM_BEGIN("preset",m_preset);
        SERIALIZE_ENUM_VAR(ReverbPreset_DEFAULT);
        SERIALIZE_ENUM_VAR(ReverbPreset_GENERIC);
        SERIALIZE_ENUM_VAR(ReverbPreset_PADDEDCELL);
        SERIALIZE_ENUM_VAR(ReverbPreset_ROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_BATHROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_LIVINGROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_STONEROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_AUDITORIUM);
        SERIALIZE_ENUM_VAR(ReverbPreset_CONCERTHALL);
        SERIALIZE_ENUM_VAR(ReverbPreset_CAVE);
        SERIALIZE_ENUM_VAR(ReverbPreset_ARENA);
        SERIALIZE_ENUM_VAR(ReverbPreset_HANGAR);
        SERIALIZE_ENUM_VAR(ReverbPreset_CARPETEDHALLWAY);
        SERIALIZE_ENUM_VAR(ReverbPreset_HALLWAY);
        SERIALIZE_ENUM_VAR(ReverbPreset_STONECORRIDOR);
        SERIALIZE_ENUM_VAR(ReverbPreset_ALLEY);
        SERIALIZE_ENUM_VAR(ReverbPreset_FOREST);
        SERIALIZE_ENUM_VAR(ReverbPreset_CITY);
        SERIALIZE_ENUM_VAR(ReverbPreset_MOUNTAINS);
        SERIALIZE_ENUM_VAR(ReverbPreset_QUARRY);
        SERIALIZE_ENUM_VAR(ReverbPreset_PLAIN);
        SERIALIZE_ENUM_VAR(ReverbPreset_PARKINGLOT);
        SERIALIZE_ENUM_VAR(ReverbPreset_SEWERPIPE);
        SERIALIZE_ENUM_VAR(ReverbPreset_UNDERWATER);
        SERIALIZE_ENUM_VAR(ReverbPreset_SMALLROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_MEDIUMROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_LARGEROOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_MEDIUMHALL);
        SERIALIZE_ENUM_VAR(ReverbPreset_LARGEHALL);
        SERIALIZE_ENUM_VAR(ReverbPreset_PLATE);
        SERIALIZE_ENUM_VAR(ReverbPreset_CUSTOM);
        SERIALIZE_ENUM_VAR(ReverbPreset_NONE);   
    SERIALIZE_ENUM_END();
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSetBusFilter)
    SERIALIZE_MEMBER("bus",m_bus);
    SERIALIZE_MEMBER("changeFrequency",m_changeFrequency);
    SERIALIZE_MEMBER("frequency",m_frequency);
    SERIALIZE_MEMBER("changeType",m_changeType);
    SERIALIZE_ENUM_BEGIN("type",m_type);
        SERIALIZE_ENUM_VAR(FilterType_LowPass);
        SERIALIZE_ENUM_VAR(FilterType_BandPass);
        SERIALIZE_ENUM_VAR(FilterType_HighPass);
        SERIALIZE_ENUM_VAR(FilterType_Notch);
        SERIALIZE_ENUM_VAR(FilterType_None);
    SERIALIZE_ENUM_END();
    SERIALIZE_MEMBER("changeQ",m_changeQ);
    SERIALIZE_MEMBER("Q",m_Q);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventRegisterCameraSubject)
    SERIALIZE_ACTION("actionActivator", m_actionActivator);
    SERIALIZE_ACTION("actionChildren", m_actionChildren);
    SERIALIZE_MEMBER("delay", m_delay);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventCameraShake)
    SERIALIZE_MEMBER("name", m_name);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventCameraIgnoreShake)
    SERIALIZE_MEMBER("ignoreShake", m_ignoreShake);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventPadRumbleStart)
    SERIALIZE_MEMBER("name", m_name);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventPadRumbleStop)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventMetronomeSetBPM)
    SERIALIZE_MEMBER("bpm", m_bpm);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventSetFloatInput)
SERIALIZE_MEMBER("inputName",m_inputName);
SERIALIZE_MEMBER("inputValue",m_inputValue);
END_SERIALIZATION()
BEGIN_SERIALIZATION_CHILD(EventSetUintInput)
SERIALIZE_MEMBER("inputName",m_inputName);
SERIALIZE_MEMBER("inputValue",m_inputValue);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventScaleChanged)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventBossBubonHit)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventGeneric)
    SERIALIZE_MEMBER("id", m_id);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventOnLink)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventPlayMusic)
    SERIALIZE_MEMBER("metronomeType", m_metronomeType);
    SERIALIZE_MEMBER("nodeName", m_nodeName);
    SERIALIZE_MEMBER("fadeTime", m_fadeTime);
    SERIALIZE_MEMBER("volume",m_volume);
    SERIALIZE_MEMBER("playOnNext",m_playOnNext);
    SERIALIZE_MEMBER("stopAndPlay",m_stopAndPlay);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventStopMusic)
    SERIALIZE_MEMBER("metronomeType", m_metronomeType);
    SERIALIZE_MEMBER("fadeTime", m_fadeTime);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventResetAfterFxAlpha)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventShowScoreboard)
    SERIALIZE_MEMBER("display", m_display);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventGoToMainMenu)
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventBusMix)
SERIALIZE_MEMBER("activate",m_activate);
SERIALIZE_OBJECT("busMix",m_busMix);
END_SERIALIZATION()

BEGIN_SERIALIZATION_CHILD(EventPlayMovie)
SERIALIZE_MEMBER("play",m_play);
END_SERIALIZATION()

}; //namespace ITF
